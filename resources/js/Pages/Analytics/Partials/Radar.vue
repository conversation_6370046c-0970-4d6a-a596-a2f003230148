<template>
	<div class="chart card card-flush">
		<div class="card-header">
			<div class="card-title">
				<div class="card-label fs-3">
					{{ $t(title) }}
					<Tooltip v-if="description" :content="$t(description)" />
				</div>
			</div>
			<div class="card-toolbar">
				<div
					v-if="showDataSelector && allData.length > 1"
					class="dropdown position-relative">
					<button
						class="btn btn-sm btn-outline-secondary dropdown-toggle text-capitalize"
						type="button"
						@click="showDropdown = !showDropdown">
						{{ $t(serieName) }} ({{ visibleDataCount }}/{{ allData?.length }})
					</button>
					<ul
						v-if="showDropdown"
						v-click-away="handleClickAway"
						class="dropdown-menu show position-absolute mt-2 top-100 end-0"
						style="z-index: 1000">
						<li v-for="dataItem in allData" :key="dataItem.name">
							<div
								class="form-check form-check-custom form-check-primary form-check-sm">
								<label
									class="dropdown-item d-flex align-items-center"
									style="cursor: pointer">
									<input
										type="checkbox"
										class="form-check-input me-2"
										:checked="visibleData[dataItem.name]"
										@change="toggleData(dataItem.name)" />
									{{ getDataLabel(dataItem.name) }}
								</label>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>

		<div class="card-body pt-0">
			<div
				v-if="failed"
				class="text-danger h-100 d-flex align-items-center justify-content-center">
				<div @click="load" style="cursor: pointer">
					<div class="my-3 d-flex align-items-center justify-content-center">
						<i class="fa-duotone fs-1 fa-rotate-right text-danger"></i>
					</div>
					<div class="my-3">
						{{ $t('general.try-again') }}
					</div>
				</div>
			</div>

			<div v-else-if="emptyData" class="row justify-content-center h-100 w-100">
				<EmptyResults class="col-lg-4 col-sm-6" />
			</div>

			<VChart v-else :option="options" autoresize></VChart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { RadarChart } from 'echarts/charts';
import { TooltipComponent } from 'echarts/components';
import EmptyResults from '@/Components/EmptyResults.vue';
import Tooltip from '@/Components/Tooltip.vue';

use([CanvasRenderer, RadarChart, TooltipComponent]);

export default {
	components: {
		VChart,
		EmptyResults,
		Tooltip
	},
	data() {
		return {
			allData: [],
			visibleData: {},
			failed: true,
			emptyData: false,
			showDropdown: false,
			colors: [
				'#5B2C87',
				'#7C3AAD',
				'#9D47D4',
				'#6366F1',
				'#4F46E5',
				'#4338CA',
				'#3730A3',
				'#312E81',
				'#2563EB',
				'#1D4ED8'
			]
		};
	},
	props: {
		title: {
			type: String,
			required: true
		},
		url: {
			type: String,
			required: true
		},
		description: {
			type: String,
			required: false
		},
		filters: {
			type: Object,
			required: false
		},
		symbol: {
			type: String,
			required: true
		},
		maxValue: {
			type: Number,
			default: null
		},
		showDataSelector: {
			type: Boolean,
			default: true
		},
		serieName: {
			type: String,
			required: false
		}
	},
	computed: {
		value() {
			return this.allData.filter((item) => this.visibleData[item.name]);
		},

		visibleDataCount() {
			return Object.values(this.visibleData).filter(Boolean).length;
		},
		indicators() {
			if (!this.allData.length) return [];

			return this.allData[0].indicators.map((indicator) => ({
				name: indicator.name[0].toUpperCase() + indicator.name.substring(1),
				max: this.maxValue || indicator.max || 100
			}));
		},
		options() {
			if (!this.value.length) return {};

			return {
				tooltip: {
					trigger: 'item',
					formatter: (params) => {
						const data = params.data;
						let tooltipContent = `<strong>${data.name}</strong><br/>`;
						data.value.forEach((val, index) => {
							tooltipContent += `${this.indicators[index].name}: ${val} ${this.symbol}<br/>`;
						});
						return tooltipContent;
					}
				},
				color: this.colors,
				radar: {
					indicator: this.indicators,
					radius: '70%',
					splitNumber: 5,
					axisLine: {
						lineStyle: {
							color: '#e5e5e5'
						}
					},
					splitLine: {
						lineStyle: {
							color: '#e5e5e5'
						}
					},
					splitArea: {
						show: true,
						areaStyle: {
							color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)']
						}
					},
					axisLabel: {
						color: '#000',
						fontSize: 14,
						fontWeight: 'bold'
					},
					axisName: {
						color: '#2c3e50',
						fontSize: 12
					}
				},
				series: [
					{
						name: 'Radar',
						type: 'radar',
						data: this.value.map((item, index) => ({
							value: item.values,
							name: item.name,
							areaStyle: {
								color: this.colorWithOpacity(
									this.colors[index % this.colors.length],
									0.3
								)
							},
							lineStyle: {
								color: this.colors[index % this.colors.length],
								width: 2
							},
							itemStyle: {
								color: this.colors[index % this.colors.length]
							}
						}))
					}
				]
			};
		}
	},
	watch: {
		filters(newValue) {
			this.allData = [];
			this.visibleData = {};
			this.emptyData = false;
			this.load();
		}
	},
	methods: {
		load() {
			this.failed = false;
			window.axios
				.get(this.url, {
					params: {
						filters: this.filters
					}
				})
				.then(({ data }) => {
					this.allData = data.report;
					this.emptyData = data.report.length == 0;

					this.initializeVisibility();
				})
				.catch(() => (this.failed = true));
		},

		initializeVisibility() {
			const visibility = {};
			this.allData.forEach((item) => {
				visibility[item.name] = true;
			});
			this.visibleData = visibility;
		},

		toggleData(dataName) {
			this.visibleData = {
				...this.visibleData,
				[dataName]: !this.visibleData[dataName]
			};
		},

		getDataLabel(dataName) {
			return this.$t ? this.$t(dataName) : dataName;
		},

		colorWithOpacity(color, opacity) {
			const rgba = `rgba(${parseInt(color.slice(1, 3), 16)}, ${parseInt(
				color.slice(3, 5),
				16
			)}, ${parseInt(color.slice(5, 7), 16)}, ${opacity})`;
			return rgba;
		},
		handleClickAway() {
			this.showDropdown = false;
		}
	},
	mounted() {
		this.load();

		document.addEventListener('click', (e) => {
			if (!this.$el.contains(e.target)) {
				this.showDropdown = false;
			}
		});
	}
};
</script>

<style scoped>
.chart {
	height: 30rem;
}

.dropdown-menu {
	max-height: 300px;
	overflow-y: auto;
}
</style>
