<script>
import { Head } from '@inertiajs/vue3';
import BaseLayout from '../../Layouts/MainLayout/BaseLayout.vue';
import HomeToolbar from '@/Pages/Home/Partials/HomeToolbar.vue';
import StatisticsSection from '@/Pages/Home/Partials/StatisticsSection.vue';
import ChartsSection from '@/Pages/Home/Partials/ChartsSection.vue';
import RidesSection from '@/Pages/Home/Partials/RidesSection.vue';
import TodayIncidentsWidget from '@/Pages/Home/Partials/TodayIncidentsWidget.vue';
import TodayAbsencesWidget from '@/Pages/Home/Partials/TodayAbsencesWidget.vue';
import DriversWidget from '@/Pages/Home/Partials/DriversWidget.vue';
import VehiclesWidget from '@/Pages/Home/Partials/VehiclesWidget.vue';
import AssistantsWidget from '@/Pages/Home/Partials/AssistantsWidget.vue';
import ReviewsOverviewWidget from '@/Pages/Home/Partials/ReviewsOverviewWidget.vue';

export default {
	components: {
		HomeToolbar,
		BaseLayout,
		Head,
		StatisticsSection,
		ChartsSection,
		RidesSection,
		TodayIncidentsWidget,
		TodayAbsencesWidget,
		DriversWidget,
		VehiclesWidget,
		AssistantsWidget,
		ReviewsOverviewWidget
	},
	props: {
		rides: {
			type: Object,
			required: true
		},
		today_incidents: {
			type: Array,
			required: true
		},
		today_absences: {
			type: Array,
			required: true
		},
		top_drivers: {
			type: Array,
			required: true
		},
		top_vehicles: {
			type: Array,
			required: true
		},
		top_assistants: {
			type: Array,
			required: true
		},
		recent_reviews: {
			type: Array,
			required: true
		},
		dashboard_stats: {
			type: Object,
			required: true
		}
	}
};
</script>

<template>
	<BaseLayout>
		<template v-slot:toolbar>
			<HomeToolbar />
		</template>

		<Head :title="$t('nav.dashboard')"></Head>

		<StatisticsSection :stats="dashboard_stats" />

		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-4">
				<TodayIncidentsWidget
					:incidents="today_incidents"
					:count="dashboard_stats.incidents_today_count" />
			</div>
			<div class="col-12 col-lg-4">
				<TodayAbsencesWidget
					:absences="today_absences"
					:count="dashboard_stats.absences_today_count" />
			</div>
			<div class="col-12 col-lg-4">
				<RidesSection :rides="rides" />
			</div>
		</div>

		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-4">
				<ReviewsOverviewWidget :reviews="recent_reviews" />
			</div>
			<div class="col-12 col-lg-8">
				<ChartsSection />
			</div>
		</div>

		<div class="row g-3 mb-4">
			<div class="col-12 col-lg-4">
				<VehiclesWidget :vehicles="top_vehicles" />
			</div>

			<div class="col-12 col-lg-4">
				<DriversWidget :drivers="top_drivers" />
			</div>
			<div class="col-12 col-lg-4">
				<AssistantsWidget :assistants="top_assistants" />
			</div>
		</div>
	</BaseLayout>
</template>
