<template>
	<div class="card card-flush h-100">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center text-capitalize">
					<i class="fa-duotone fa-brake-warning me-4 fs-1 text-primary"></i>
					{{ $t('incident.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('incidents.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<div class="d-flex align-items-center mb-3">
				<div class="fs-2x fw-bold text-danger me-3">{{ count }}</div>
				<div class="text-gray-600 fs-6">{{ $t('incident.today_count') }}</div>
			</div>

			<template v-if="incidents.length > 0">
				<div class="separator my-3"></div>
				<div class="">
					<template v-for="(incident, index) in incidents" :key="incident.id">
						<div v-if="index > 0" class="separator my-2"></div>
						<div class="d-flex align-items-center py-2">
							<div class="symbol symbol-35px me-3">
								<div class="symbol-label bg-light-primary">
									<i class="fa-duotone fa-brake-warning fs-4 text-primary"></i>
								</div>
							</div>
							<div class="flex-grow-1">
								<div class="d-flex justify-content-between align-items-start">
									<div>
										<div class="text-gray-800 fw-semibold fs-6">
											#{{ incident.id }}
										</div>
										<div class="text-gray-600 fs-7 text-capitalize">
											{{ $t(`general.enums.${incident.type}`) }}
										</div>
									</div>
									<div class="text-end">
										<div v-if="incident.reporter" class="text-gray-600 fs-7">
											{{ incident.reporter.name }}
										</div>
										<div class="text-gray-500 fs-8">
											{{ incident.created_at_diff }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</template>
				</div>
			</template>

			<EmptyResults v-else>
				<i class="fa-duotone fa-check-circle fs-2x text-success mb-2"></i>
				<div class="text-gray-600 fs-6">{{ $t('incident.no_incidents_today') }}</div>
			</EmptyResults>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';

export default {
	name: 'TodayIncidentsWidget',
	components: {
		Link,
		EmptyResults
	},
	props: {
		incidents: {
			type: Array,
			required: true
		},
		count: {
			type: Number,
			required: true
		}
	}
};
</script>
