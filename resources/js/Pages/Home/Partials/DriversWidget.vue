<template>
	<div class="card card-flush h-100">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center text-capitalize">
					<i class="fa-duotone fa-steering-wheel me-4 fs-1 text-primary"></i>
					{{ $t('fleet.driver.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('fleet.drivers.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<template v-if="drivers.length > 0">
				<div class="table-responsive">
					<table class="table table-row-dashed align-middle gs-0 gy-3 my-0">
						<tbody>
							<tr v-for="(driver, index) in drivers" :key="driver.id">
								<td class="p-0">
									<div class="d-flex align-items-center py-2">
										<div class="symbol symbol-40px me-3">
											<img :src="driver.image" :alt="driver.name" />
										</div>
										<div class="flex-grow-1">
											<Link :href="route('fleet.drivers.view', driver.id)">
												<div
													class="text-gray-800 text-hover-primary fw-semibold fs-6">
													{{ driver.name }}
												</div>
											</Link>
											<div class="text-gray-600 fs-7">
												{{ $t('fleet.driver.name') }}
											</div>
										</div>
										<div class="text-end">
											<div
												class="badge text-capitalize"
												:class="statusBadgeClass(driver.status)">
												{{ $t(`general.enums.${driver.status}`) }}
											</div>
										</div>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</template>

			<EmptyResults v-else>
				<i class="fa-duotone fa-steering-wheel fs-2x text-gray-400 mb-2"></i>
				<div class="text-gray-600 fs-6">{{ $t('fleet.driver.no_drivers') }}</div>
			</EmptyResults>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';

export default {
	name: 'DriversWidget',
	components: {
		Link,
		EmptyResults
	},
	props: {
		drivers: {
			type: Array,
			required: true
		}
	},
	methods: {
		statusBadgeClass(status) {
			switch (status) {
				case 'active':
					return 'badge-light-success';
				case 'inactive':
					return 'badge-light-dark';
				default:
					return 'badge-light-secondary';
			}
		}
	}
};
</script>
