<template>
	<div class="card card-flush h-100">
		<div class="card-header p-3">
			<div class="card-title">
				<div class="card-label fw-semibold fs-3 d-flex align-items-center text-capitalize">
					<i class="fa-duotone fa-calendar-xmark me-4 fs-1 text-primary"></i>
					{{ $t('plan.absence.names') }}
				</div>
			</div>
			<div class="card-toolbar">
				<Link
					:href="route('plans.absences.list')"
					class="btn btn-link-primary font-weight-bold px-0 text-hover-primary fs-7 fw-bold">
					{{ $t('general.see_all') }}
					<i class="fa-duotone fa-arrow-right ms-1 text-hover-primary"></i>
				</Link>
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<div class="d-flex align-items-center mb-3">
				<div class="fs-2x fw-bold text-warning me-3">{{ count }}</div>
				<div class="text-gray-600 fs-6">{{ $t('plan.absence.today_count') }}</div>
			</div>

			<template v-if="absences.length > 0">
				<div class="separator my-3"></div>
				<div class="">
					<template v-for="(absence, index) in absences" :key="absence.id">
						<div v-if="index > 0" class="separator my-2"></div>
						<div class="d-flex align-items-center py-2">
							<div class="symbol symbol-35px me-3">
								<img
									v-if="absence.passenger?.image"
									:src="absence.passenger.image"
									:alt="absence.passenger.name"
									class="img-fluid" />
								<div v-else class="symbol-label bg-light-warning">
									<i class="fa-duotone fa-user fs-4 text-warning"></i>
								</div>
							</div>
							<div class="flex-grow-1">
								<div class="text-gray-800 fw-semibold fs-6">
									{{ absence.passenger?.name || $t('general.profile_not_found') }}
								</div>
								<div class="text-gray-600 fs-7">
									{{ $t(`general.enums.${absence.reason}`) }}
								</div>
								<div class="text-gray-500 fs-8">
									{{ formatDate(absence.from) }} - {{ formatDate(absence.to) }}
								</div>
							</div>
						</div>
					</template>
				</div>
			</template>

			<EmptyResults v-else>
				<i class="fa-duotone fa-check-circle fs-2x text-success mb-2"></i>
				<div class="text-gray-600 fs-6">{{ $t('plan.absence.no_absences_today') }}</div>
			</EmptyResults>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import EmptyResults from '@/Components/EmptyResults.vue';

export default {
	name: 'TodayAbsencesWidget',
	components: {
		Link,
		EmptyResults
	},
	props: {
		absences: {
			type: Array,
			required: true
		},
		count: {
			type: Number,
			required: true
		}
	},
	methods: {
		formatDate(date) {
			return new Date(date).toLocaleDateString();
		}
	}
};
</script>
