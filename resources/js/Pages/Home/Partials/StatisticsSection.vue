<template>
	<div class="row g-3 mb-4">
		<div v-for="(stat, index) in statisticsData" :key="index" class="col-sm-6 col-lg-3">
			<State
				:title="stat.title"
				:symbol="stat.symbol"
				:url="stat.url"
				:icon="stat.icon"
				:withChart="false"
				:filters="filters"
				class="h-100" />
		</div>
	</div>
</template>

<script>
import State from '@/Pages/Analytics/Partials/State.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'StatisticsSection',
	components: {
		State
	},
	props: {
		stats: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			filters: {}
		};
	},
	computed: {
		statisticsData() {
			return [
				{
					title: trans('analytics.charts.total_number_of_passengers.title'),
					url: route('analytics.report.transport', {
						key: 'total-number-of-passengers'
					}),
					symbol: '',
					icon: 'fa-person-seat'
				},
				{
					title: trans('analytics.charts.total_driver_count.title'),
					url: route('analytics.report.fleet', {
						key: 'total-driver-count'
					}),
					symbol: '',
					icon: 'fa-steering-wheel'
				},
				{
					title: trans('analytics.charts.total_vehicle_count.title'),
					url: route('analytics.report.fleet', {
						key: 'total-vehicle-count'
					}),
					symbol: '',
					icon: 'fa-shuttle-van'
				},
				{
					title: trans('analytics.charts.total_assistant_count.title'),
					url: route('analytics.report.fleet', {
						key: 'total-assistant-count'
					}),
					symbol: '',
					icon: 'fa-handshake-angle'
				}
			];
		}
	}
};
</script>
