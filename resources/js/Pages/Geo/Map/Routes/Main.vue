<template>
	<BaseLayout :content-width-fluid="false" background-color="rgb(255, 255, 255)" fullMode>
		<Head :title="$t('nav.routes')" />

		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<div class="d-flex flex-column flex-fill h-100">
			<div class="position-relative h-100">
				<!-- Map Container -->
				<div class="h-100">
					<DynamicMap
						v-if="!isLoading && (selectedRoute || visibleRoutes.length > 0)"
						:routes="selectedRoute ? [selectedRoute] : visibleRoutes"
						class="w-100 h-100"
						mode="routes-map"
						:rounded="false"
						:shouldFitBound="!isToggleVisibilityAction" />
					<!-- Empty Map when no routes are visible -->
					<div
						v-if="
							!isLoading &&
							!selectedRoute &&
							visibleRoutes.length === 0 &&
							filteredRoutes.length > 0
						"
						class="w-100 h-100 d-flex justify-content-center align-items-center bg-light">
						<div class="text-center">
							<i class="fa-duotone fa-eye-slash fs-1 text-muted mb-3 d-block"></i>
							<h5>{{ $t('transport.route.all_routes_hidden') }}</h5>
							<p class="text-muted">
								{{ $t('transport.route.click_eye_to_show') }}
							</p>
							<button @click="showAllRoutes" class="btn btn-sm btn-primary">
								<i class="fa-duotone fa-eye me-2"></i>
								{{ $t('transport.route.show_all_routes') }}
							</button>
						</div>
					</div>
					<div
						v-if="isLoading"
						class="d-flex justify-content-center align-items-center h-100">
						<LoadingSpinner />
					</div>
				</div>

				<!-- Search Bar Overlay -->
				<div
					v-if="!selectedRoute"
					class="position-absolute top-0 start-0 m-4"
					style="width: 300px">
					<RoutesFilters
						:pending-filters="pendingFilters"
						@update:pending-filters="updatePendingFilters"
						@apply-filters="applyFilters" />
				</div>

				<!-- Side Panel Overlay -->
				<div
					v-if="selectedRoute"
					class="position-absolute top-0 start-0 h-100"
					style="width: 22vw; min-width: 300px">
					<RouteSidePanel :singleRoute="selectedRoute" @close="hideRouteInfo" />
				</div>

				<!-- Routes Carousel -->
				<div
					v-if="!selectedRoute && filteredRoutes.length > 0"
					class="position-absolute top-0 start-0 mt-15 overflow-auto custom-height scroll">
					<div class="d-flex flex-column gap-2 py-4 px-4">
						<RouteMapCard
							v-for="(route, index) in filteredRoutes"
							:key="route.id"
							:singleRoute="route"
							:color-index="index"
							:is-visible="isRouteVisible(route.id)"
							@click="showRouteInfo(route)"
							@toggle-visibility="toggleRouteVisibility(route.id)" />
					</div>
				</div>

				<!-- Empty State Overlay -->
				<div
					v-if="!isLoading && !selectedRoute && filteredRoutes.length === 0"
					class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-flex justify-content-center align-items-center">
					<div class="bg-white p-4 rounded shadow text-center">
						<i class="fa-duotone fa-map-marker-slash fs-1 text-muted mb-3 d-block"></i>
						<h5>{{ $t('transport.route.no_routes_found') }}</h5>
						<p class="text-muted">
							{{ $t('transport.route.no_routes_match_filters') }}
						</p>
						<button @click="clearAllFilters" class="btn btn-sm btn-primary">
							{{ $t('general.clear_filters') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>
<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import DynamicMap from '@/Components/Location/DynamicMap.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import { trans } from 'laravel-vue-i18n';
import { Head } from '@inertiajs/vue3';
import RoutesFilters from './Partials/RoutesFilters.vue';
import RouteMapCard from './Partials/RouteMapCard.vue';
import RouteSidePanel from './RouteSidePanel/Main.vue';
import LoadingSpinner from '@/Components/LoadingSpinner.vue';

export default {
	name: 'Main',
	components: {
		BaseLayout,
		Head,
		HeaderMenu,
		DynamicMap,
		RoutesFilters,
		RouteMapCard,
		RouteSidePanel,
		LoadingSpinner
	},
	props: {
		routes: {
			type: Array,
			required: true
		},
		filters: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			isLoading: false,
			selectedRoute: null,
			visibleRouteIds: new Set(),
			allRoutesHiddenManually: false,
			isToggleVisibilityAction: false,
			pendingFilters: {
				search: this.filters.search || '',
				status: this.filters.status || null,
				region_id: this.filters.region_id || null,
				static: this.filters.static || null
			},
			activeFilters: {
				search: this.filters.search || '',
				status: this.filters.status || null,
				region_id: this.filters.region_id || null,
				static: this.filters.static || null
			},
			menu: [
				{
					hasPermission: true,
					active: route().current('geo.live-map'),
					route: route('geo.live-map'),
					icon: 'fa-duotone fa-location-crosshairs',
					content: trans('nav.live_map')
				},
				{
					hasPermission: true,
					active: route().current('geo.passengers-map'),
					route: route('geo.passengers-map'),
					icon: 'fa-duotone fa-person-seat-reclined',
					content: trans('transport.passenger.names')
				},
				{
					hasPermission: true,
					active: route().current('geo.routes-map'),
					route: route('geo.routes-map'),
					icon: 'fa-duotone fa-route',
					content: trans('transport.route.names')
				}
			]
		};
	},
	computed: {
		filteredRoutes() {
			const filtered = this.routes.filter((route) => {
				if (this.activeFilters.search && route.name) {
					const searchTerm = this.activeFilters.search.toLowerCase();
					const routeName = route.name.toLowerCase();
					if (!routeName.includes(searchTerm)) {
						return false;
					}
				}

				if (this.activeFilters.status && route.status !== this.activeFilters.status) {
					return false;
				}

				if (
					this.activeFilters.region_id &&
					route.region_id !== this.activeFilters.region_id
				) {
					return false;
				}

				if (
					this.activeFilters.static !== null &&
					route.static !== this.activeFilters.static
				) {
					return false;
				}

				return true;
			});

			if (
				this.visibleRouteIds.size === 0 &&
				filtered.length > 0 &&
				!this.allRoutesHiddenManually
			) {
				filtered.forEach((route) => this.visibleRouteIds.add(route.id));
			}

			return filtered;
		},

		visibleRoutes() {
			return this.filteredRoutes.filter((route) => this.visibleRouteIds.has(route.id));
		}
	},
	methods: {
		updatePendingFilters(newFilters) {
			this.pendingFilters = newFilters;
		},
		applyFilters() {
			this.isLoading = true;
			this.activeFilters = { ...this.pendingFilters };

			setTimeout(() => {
				this.isLoading = false;
			}, 300);
		},
		clearAllFilters() {
			this.pendingFilters = {
				search: '',
				status: null,
				region_id: null,
				static: null
			};
			this.activeFilters = { ...this.pendingFilters };
			this.allRoutesHiddenManually = false;

			this.visibleRouteIds.clear();
			this.filteredRoutes.forEach((route) => this.visibleRouteIds.add(route.id));
		},
		showRouteInfo(route) {
			this.selectedRoute = route;
		},
		hideRouteInfo() {
			this.selectedRoute = null;
		},
		toggleRouteVisibility(routeId) {
			this.isToggleVisibilityAction = true;

			if (this.visibleRouteIds.has(routeId)) {
				this.visibleRouteIds.delete(routeId);

				if (this.visibleRouteIds.size === 0) {
					this.allRoutesHiddenManually = true;
				}
			} else {
				this.visibleRouteIds.add(routeId);
				this.allRoutesHiddenManually = false;
			}

			setTimeout(() => {
				this.isToggleVisibilityAction = false;
			}, 100);
		},
		isRouteVisible(routeId) {
			return this.visibleRouteIds.has(routeId);
		},
		showAllRoutes() {
			this.isToggleVisibilityAction = false;
			this.allRoutesHiddenManually = false;
			this.visibleRouteIds.clear();
			this.filteredRoutes.forEach((route) => this.visibleRouteIds.add(route.id));
		}
	}
};
</script>

<style scoped>
.custom-height {
	max-height: calc(100vh - 117px);
}

/* Hide scrollbar while maintaining functionality */
.overflow-auto {
	/* For Chrome, Safari, and Opera */
	&::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For IE and Edge */
	-ms-overflow-style: none;
}
</style>
