<template>
	<div
		class="card flex-shrink-0 bg-white bg-opacity-90 shadow-xm border-2"
		:class="{ 'border-hover-primary hover-elevate-up': isVisible }"
		:style="{ 'border-color': routeColor, 'width': '210px', 'cursor': 'pointer' }"
		@click="$emit('click')">
		<div class="card-body p-2">
			<div class="d-flex justify-content-between align-items-center mb-1">
				<div class="d-flex align-items-center">
					<span
						class="fw-bold text-gray-800 fs-7 text-truncate"
						style="max-width: 140px"
						>{{ singleRoute.name }}</span
					>
				</div>
				<button
					v-if="showVisibiltyToggle"
					class="btn btn-icon btn-xs btn-light-primary btn-circle w-25px h-25px"
					@click.stop="toggleVisibility"
					:class="{ 'btn-active-light-primary': isVisible }">
					<i class="fs-6 fa-duotone" :class="isVisible ? 'fa-eye' : 'fa-eye-slash'"></i>
				</button>
			</div>
			<!-- 
			<div class="d-flex flex-column gap-1 mb-1">
				<div class="d-flex align-items-center">
					<i class="fa-duotone fa-circle-dot text-success fs-8 me-1"></i>
					<span class="fs-8 text-truncate" style="max-width: 170px">{{
						singleRoute.origin?.name
					}}</span>
				</div>
				<div class="d-flex align-items-center">
					<i class="fa-duotone fa-circle-dot text-primary fs-8 me-1"></i>
					<span class="fs-8 text-truncate" style="max-width: 170px">{{
						singleRoute.destination?.name
					}}</span>
				</div>
			</div> -->

			<div class="d-flex align-items-center justify-content-between mt-1">
				<div class="d-flex align-items-center gap-2">
					<div class="d-flex align-items-center">
						<i class="fa-duotone fa-route text-muted fs-8 me-1"></i>
						<span class="fs-8">{{ formatDistance(singleRoute.distance) }}</span>
					</div>
					<div class="d-flex align-items-center">
						<i class="fa-duotone fa-users text-muted fs-8 me-1"></i>
						<span class="fs-8">{{ singleRoute.passengers_count }}</span>
					</div>
					<div v-if="singleRoute.static" class="d-flex align-items-center">
						<i class="fa-duotone fa-thumbtack text-muted fs-8"></i>
					</div>
				</div>
				<span class="badge badge-sm badge-light-primary text-capitalize fs-9">
					{{ $t(`general.enums.${singleRoute.status}`) }}</span
				>
			</div>
		</div>
	</div>
</template>

<script>
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'RouteMapCard',
	props: {
		singleRoute: {
			type: Object,
			required: true
		},
		colorIndex: {
			type: Number,
			default: 0
		},
		isVisible: {
			type: Boolean,
			default: true
		},
		showVisibiltyToggle: {
			type: Boolean,
			default: true
		}
	},
	computed: {
		routeColor() {
			const colors = [
				'#6210cc', // Primary color
				'#009ef7', // Info color
				'#50cd89', // Success color
				'#f1416c', // Danger color
				'#7239ea', // Purple color
				'#ffc700', // Warning color
				'#181c32', // Dark color
				'#e4e6ef' // Light color
			];
			return this.isVisible ? colors[this.colorIndex % colors.length] : 'transparent';
		}
	},
	methods: {
		formatDistance(distance) {
			if (!distance) return '-';
			return `${(distance / 1000).toFixed(1)} ` + trans('transport.route.labels.km');
		},
		toggleVisibility() {
			this.$emit('toggle-visibility');
		}
	}
};
</script>
