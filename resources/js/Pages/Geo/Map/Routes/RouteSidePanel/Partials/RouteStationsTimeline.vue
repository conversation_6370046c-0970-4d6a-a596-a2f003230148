<template>
	<div class="timeline-container w-100">
		<ul class="tl w-100 px-3 pt-3">
			<li
				v-for="(station, index) in sortedStations"
				:key="index"
				class="d-flex align-items-start border-start position-relative border-2 ps-8"
				style="min-height: 20px"
				:class="
					index === sortedStations.length - 1 ? 'border-transparent' : 'border-black pb-3'
				">
				<div class="position-absolute top-0 start-0 translate-middle mt-2 bg-white">
					<i class="fas fa-dot-circle fs-7" :class="getIconClass(index)"></i>
				</div>
				<div class="d-flex justify-content-between align-items-center flex-grow-1">
					<div class="fs-7">
						{{ station.name || station.location?.name }}
					</div>
					<div class="fw-normal text-gray-600 fs-8 ms-2">
						<template v-if="typeof station.order === 'number'"
							>#{{ station.order }}</template
						>
						<template v-else
							><span class="text-capitalize">{{ station.order }}</span></template
						>
					</div>
				</div>
			</li>
		</ul>
	</div>
</template>

<script>
import Tooltip from '@/Components/Tooltip.vue';

export default {
	name: 'RouteStationsTimeline',
	components: {
		Tooltip
	},
	props: {
		stations: {
			type: Array,
			required: true
		}
	},
	computed: {
		sortedStations() {
			const stationsCopy = [...this.stations];

			// Find and extract origin and destination stations
			const originIndex = stationsCopy.findIndex((s) => s.isOrigin);
			const origin = originIndex !== -1 ? stationsCopy.splice(originIndex, 1)[0] : null;

			const destIndex = stationsCopy.findIndex((s) => s.isDestination);
			const destination = destIndex !== -1 ? stationsCopy.splice(destIndex, 1)[0] : null;

			// Sort the remaining stations by order
			const sortedRegularStations = stationsCopy.sort((a, b) => a.order - b.order);

			// Combine all stations in the correct order
			const result = [];
			if (origin) result.push(origin);
			result.push(...sortedRegularStations);
			if (destination) result.push(destination);

			return result;
		}
	},
	methods: {
		getIconClass(index) {
			const station = this.sortedStations[index];

			if (station.isOrigin) {
				return 'text-success';
			} else if (station.isDestination) {
				return 'text-primary';
			} else if (index === 0) {
				return 'text-success';
			} else if (index === this.sortedStations.length - 1) {
				return 'text-primary';
			}
			return 'text-info';
		},
		getPassengerNames(passengers) {
			return passengers.map((p) => p.name).join(' - ');
		},
		getVisiblePassengers(passengers) {
			if (!passengers || passengers.length <= 3) {
				return passengers || [];
			}
			return passengers.slice(0, 2);
		}
	}
};
</script>
