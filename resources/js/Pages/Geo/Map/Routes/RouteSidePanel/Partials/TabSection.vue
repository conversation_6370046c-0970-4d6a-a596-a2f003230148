<template>
	<Tab :tabs="tabs" :activeTab="activeTab">
		<template #overview>
			<OverviewTab :singleRoute="singleRoute" />
		</template>
		<template #stations>
			<StationsTab :stations="singleRoute.stations" :singleRoute="singleRoute" />
		</template>
	</Tab>
</template>

<script>
import Tab from '@/Components/General/Tabs/Tab.vue';
import OverviewTab from './OverviewTab.vue';
import StationsTab from './StationsTab.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	components: {
		Tab,
		OverviewTab,
		StationsTab
	},
	props: {
		singleRoute: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			tabs: [
				{ id: 'overview', label: trans('general.overview'), icon: 'fa-duotone fa-info' },
				{
					id: 'stations',
					label: trans('transport.station.names'),
					icon: 'fa-duotone fa-location-dot'
				}
			],
			activeTab: 'overview'
		};
	}
};
</script>
