<template>
	<div class="card border-0 p-0 custom-height scroll scroll-pull">
		<div class="card-body px-0 py-0">
			<RouteStationsTimeline :stations="stationsWithEndpoints" />
		</div>
	</div>
</template>

<script>
import RouteStationsTimeline from './RouteStationsTimeline.vue';
import { trans } from 'laravel-vue-i18n';

export default {
	name: 'StationsTab',
	components: {
		RouteStationsTimeline
	},
	props: {
		stations: {
			type: Array,
			required: true
		},
		singleRoute: {
			type: Object,
			required: true
		}
	},
	computed: {
		stationsWithEndpoints() {
			const result = [];

			if (this.singleRoute.origin) {
				result.push({
					name: this.singleRoute.origin.name,
					location: this.singleRoute.origin,
					order: trans('transport.route.origin.name'),
					isOrigin: true
				});
			}

			result.push(...this.stations);

			if (this.singleRoute.destination) {
				result.push({
					name: this.singleRoute.destination.name,
					location: this.singleRoute.destination,
					order: trans('transport.route.destination.name'),
					isDestination: true
				});
			}

			return result;
		}
	}
};
</script>

<style scoped>
.custom-height {
	max-height: calc(100vh - 300px);
	overflow-y: auto;
}

.custom-height::-webkit-scrollbar {
	width: 5px;
}

.custom-height::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 10px;
}

.custom-height::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 10px;
}

.custom-height::-webkit-scrollbar-thumb:hover {
	background: #555;
}
</style>
