<template>
	<div class="d-flex flex-column gap-2 p-4 side-bar">
		<div class="card shadow-sm">
			<div class="card-body">
				<div class="d-flex w-100 justify-content-between align-items-start">
					<div>
						<div
							class="fs-5 text-gray-900 fw-semibold text-capitalize d-flex align-items-start">
							{{ singleRoute.name }}
							<div class="ms-3 fs-4 text-gray-600">
								<span class="badge text-capitalize" :class="statusBadgeClass">
									{{ $t(`general.enums.${singleRoute.status}`) }}
								</span>
							</div>
						</div>
						<div class="text-gray-600 fw-semibold mt-2">
							<i class="fad fa-hashtag text-primary me-2"></i>
							{{ singleRoute.reference || $t('general.not_available') }}
						</div>
					</div>
					<div>
						<button
							@click.prevent="closeSidePanel"
							class="btn btn-sm btn-icon btn-circle btn-light-primary btn-outline ms-5">
							<i class="fa-solid fa-times fs-2"></i>
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="card shadow-sm">
			<div class="card-body">
				<TabSection :singleRoute="singleRoute" />
			</div>
		</div>
	</div>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import TabSection from './Partials/TabSection.vue';

export default {
	components: {
		Link,
		TabSection
	},
	props: {
		singleRoute: {
			type: Object,
			required: true
		}
	},
	emits: ['close'],
	computed: {
		statusBadgeClass() {
			const classes = {
				active: 'badge-light-success',
				inactive: 'badge-light-dark'
			};
			return classes[this.singleRoute.status] || 'badge-light-secondary';
		}
	},
	methods: {
		closeSidePanel() {
			this.$emit('close');
		}
	}
};
</script>

<style scoped>
.side-bar::-webkit-scrollbar {
	display: none;
}

.side-bar {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.custom-height {
	max-height: calc(100vh - 500px);
}
</style>
