<template>
	<div class="d-flex flex-column">
		<div class="d-flex align-items-center justify-content-between w-100">
			<div class="d-flex align-items-center pb-3 w-100 input-group-sm">
				<i class="fas fa-search svg-icon svg-icon-1 position-absolute ms-6 fs-4"></i>
				<input
					type="text"
					v-model="searchTerm"
					class="form-control w-100 ps-15 border border-hover-primary"
					:placeholder="$t('general.search')"
					@keyup.enter="applySearch" />
			</div>
			<div class="pb-3 ms-3">
				<button
					@click.prevent="showFilter = !showFilter"
					class="btn btn-sm btn-flex bg-body btn-light-primary position-relative border border-primary text-capitalize">
					<i class="fas fa-filter fs-4"></i>
					{{ $t('general.filter') }}
					<span
						v-if="numberOfActiveFilters > 0"
						class="position-absolute top-0 start-100 translate-middle badge badge-circle badge-primary">
						{{ numberOfActiveFilters }}
					</span>
				</button>
			</div>
		</div>

		<Transition name="fade">
			<div
				v-if="showFilter"
				v-click-away="handleClickAway"
				class="position-absolute top-100 start-0 w-100 bg-white shadow-lg rounded-3 mt-1 z-index-3">
				<div class="px-7 py-5">
					<div class="fs-5 text-dark fw-bold text-capitalize">
						{{ $t('general.filter_options') }}
					</div>
				</div>
				<div class="separator border-gray-200"></div>
				<div class="px-7 py-5">
					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('general.status') }}
						</label>
						<MultiSelect
							v-model="localFilters.status"
							:searchable="true"
							options="PassengerStatus"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('general.gender') }}
						</label>
						<MultiSelect
							v-model="localFilters.gender"
							:searchable="true"
							options="UserGender"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('transport.route.name') }}
						</label>
						<MultiSelect
							v-model="localFilters.route_id"
							:searchable="true"
							:limit="-1"
							options="Route"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('transport.responsible.name') }}
						</label>
						<MultiSelect
							v-model="localFilters.responsible_id"
							:searchable="true"
							:limit="-1"
							options="Responsible"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('transport.region.name') }}
						</label>
						<MultiSelect
							v-model="localFilters.region_id"
							:searchable="true"
							options="Region"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="mb-5">
						<label class="form-label text-capitalize fw-semibold">
							{{ $t('transport.group.name') }}
						</label>
						<MultiSelect
							v-model="localFilters.group_id"
							:searchable="true"
							options="Group"
							@update:modelValue="emitPendingFilters" />
					</div>

					<div class="d-flex justify-content-end">
						<button
							@click="clearFilters"
							type="reset"
							class="btn btn-sm btn-light btn-active-light-primary text-capitalize me-2">
							{{ $t('general.reset') }}
						</button>
						<button
							@click="applyFilters"
							type="submit"
							class="btn btn-sm btn-primary text-capitalize">
							{{ $t('general.apply') }}
						</button>
					</div>
				</div>
			</div>
		</Transition>
	</div>
</template>

<script>
import MultiSelect from '@/Components/MultiSelect.vue';

export default {
	name: 'PassengersFilters',
	components: { MultiSelect },
	props: {
		pendingFilters: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			localFilters: { ...this.pendingFilters },
			searchTerm: this.pendingFilters.search || '',
			showFilter: false
		};
	},
	watch: {
		pendingFilters: {
			handler(newFilters) {
				this.localFilters = { ...newFilters };
				this.searchTerm = newFilters.search || '';
			},
			deep: true
		}
	},
	methods: {
		emitPendingFilters() {
			this.$emit('update:pending-filters', this.localFilters);
		},
		applyFilters() {
			this.$emit('apply-filters');
			this.showFilter = false;
		},
		applySearch() {
			this.localFilters.search = this.searchTerm;
			this.emitPendingFilters();
			this.$emit('apply-filters');
		},
		clearFilters() {
			this.localFilters = {
				status: null,
				gender: null,
				route_id: null,
				responsible_id: null,
				region_id: null,
				group_id: null,
				search: null
			};
			this.searchTerm = '';
			this.emitPendingFilters();
			this.$emit('apply-filters');
		},
		handleClickAway() {
			this.showFilter = false;
		}
	},
	computed: {
		numberOfActiveFilters() {
			// Only count actual filters, not search
			return Object.entries(this.localFilters).filter(
				([key, value]) => key !== 'search' && value !== null && value !== ''
			).length;
		}
	}
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}

.z-index-3 {
	z-index: 3;
}
</style>
