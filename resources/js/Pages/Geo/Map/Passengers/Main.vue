<template>
	<BaseLayout :content-width-fluid="false" background-color="rgb(255, 255, 255)" fullMode>
		<Head :title="$t('transport.passenger.map')" />

		<template #headerMenu>
			<HeaderMenu :menu="menu" />
		</template>

		<div class="d-flex flex-column flex-fill h-100">
			<div class="position-relative h-100">
				<!-- Map Container -->
				<div class="h-100">
					<DynamicMap
						v-if="!isLoading"
						class="w-100 h-100"
						:passengers="displayedPassengers"
						mode="passengers-map"
						:rounded="false" />
					<div v-else class="d-flex justify-content-center align-items-center h-100">
						<LoadingSpinner />
					</div>
				</div>

				<!-- Search Bar Overlay -->
				<div
					v-if="!selectedRouteId"
					class="position-absolute top-0 start-0 m-4"
					style="width: 300px">
					<PassengersFilters
						:pending-filters="pendingFilters"
						@update:pending-filters="updatePendingFilters"
						@apply-filters="applyFilters" />
				</div>

				<!-- Route Cards Carousel -->
				<div
					v-if="filteredPassengers.length > 0 && !selectedRouteId"
					class="position-absolute top-0 start-0 mt-15 overflow-auto custom-height scroll">
					<div class="d-flex flex-column gap-2 py-4 px-4">
						<RouteMapCard
							v-for="(route, index) in passengerRoutes"
							:key="route.id"
							:singleRoute="route"
							:color-index="index"
							:showVisibiltyToggle="false"
							@click="selectRoute(route)"
							class="cursor-pointer" />
					</div>
				</div>

				<!-- Selected Route Back Button -->
				<div v-if="selectedRouteId" class="position-absolute top-0 start-0 mt-4 ms-4">
					<div class="bg-white p-3 rounded shadow d-flex align-items-center gap-2">
						<button
							@click="clearSelectedRoute"
							class="btn btn-sm btn-outline-secondary">
							<i class="fa-duotone fa-arrow-left me-1"></i>
							{{ $t('general.back') }}
						</button>
						<div class="symbol-label p-1 bg-light-primary">
							<i class="fa-duotone fa-route fs-3 text-primary"></i>
						</div>
						<span class="ms-2 text-truncate">
							{{ selectedRouteName }}
						</span>
					</div>
				</div>

				<!-- Empty State Overlay -->
				<div
					v-if="!isLoading && filteredPassengers.length === 0"
					class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-flex justify-content-center align-items-center">
					<div class="bg-white p-4 rounded shadow text-center">
						<i
							class="fa-duotone fa-person-seat-reclined-slash fs-1 text-muted mb-3 d-block"></i>
						<h5>{{ $t('transport.passenger.no_passengers_found') }}</h5>
						<p class="text-muted">
							{{ $t('transport.passenger.no_passengers_match_filters') }}
						</p>
						<button @click="clearAllFilters" class="btn btn-sm btn-primary">
							{{ $t('general.clear_filters') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</BaseLayout>
</template>

<script>
import BaseLayout from '@/Layouts/MainLayout/BaseLayout.vue';
import DynamicMap from '@/Components/Location/DynamicMap.vue';
import HeaderMenu from '@/Components/HeaderMenu.vue';
import { trans } from 'laravel-vue-i18n';
import { Head, router } from '@inertiajs/vue3';
import PassengersFilters from './Partials/PassengersFilters.vue';
import RouteMapCard from '@/Pages/Geo/Map/Routes/Partials/RouteMapCard.vue';
import LoadingSpinner from '@/Components/LoadingSpinner.vue';

export default {
	name: 'Main',
	components: {
		BaseLayout,
		Head,
		HeaderMenu,
		DynamicMap,
		PassengersFilters,
		LoadingSpinner,
		RouteMapCard
	},
	props: {
		passengers: {
			type: Array,
			required: true
		},
		filters: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			isLoading: false,
			selectedRouteId: null,
			selectedRouteName: '',
			pendingFilters: {
				search: this.filters.search || '',
				status: this.filters.status || null,
				gender: this.filters.gender || null,
				route_id: this.filters.route_id || null,
				responsible_id: this.filters.responsible_id || null,
				region_id: this.filters.region_id || null,
				group_id: this.filters.group_id || null
			},
			activeFilters: {
				search: this.filters.search || '',
				status: this.filters.status || null,
				gender: this.filters.gender || null,
				route_id: this.filters.route_id || null,
				responsible_id: this.filters.responsible_id || null,
				region_id: this.filters.region_id || null,
				group_id: this.filters.group_id || null
			},
			menu: [
				{
					hasPermission: true,
					active: route().current('geo.live-map'),
					route: route('geo.live-map'),
					icon: 'fa-duotone fa-location-crosshairs',
					content: trans('nav.live_map')
				},
				{
					hasPermission: true,
					active: route().current('geo.passengers-map'),
					route: route('geo.passengers-map'),
					icon: 'fa-duotone fa-person-seat-reclined',
					content: trans('transport.passenger.names')
				},
				{
					hasPermission: true,
					active: route().current('geo.routes-map'),
					route: route('geo.routes-map'),
					icon: 'fa-duotone fa-route',
					content: trans('transport.route.names')
				}
			]
		};
	},
	computed: {
		filteredPassengers() {
			return this.passengers.filter((passenger) => {
				// Filter by search term
				if (this.activeFilters.search && passenger.name) {
					const searchTerm = this.activeFilters.search.toLowerCase();
					const passengerName = passenger.name.toLowerCase();
					if (!passengerName.includes(searchTerm)) {
						return false;
					}
				}

				// Filter by status
				if (this.activeFilters.status && passenger.status !== this.activeFilters.status) {
					return false;
				}

				// Filter by gender
				if (this.activeFilters.gender && passenger.gender !== this.activeFilters.gender) {
					return false;
				}

				// Filter by responsible
				if (
					this.activeFilters.responsible_id &&
					!passenger.responsibles?.some((r) => r.id === this.activeFilters.responsible_id)
				) {
					return false;
				}

				// Filter by route
				if (
					this.activeFilters.route_id &&
					!passenger.routes?.some((r) => r.id === this.activeFilters.route_id)
				) {
					return false;
				}

				// Filter by region
				if (
					this.activeFilters.region_id &&
					passenger.region_id !== this.activeFilters.region_id
				) {
					return false;
				}

				// Filter by group
				if (
					this.activeFilters.group_id &&
					passenger.group_id !== this.activeFilters.group_id
				) {
					return false;
				}

				return true;
			});
		},
		passengerRoutes() {
			const uniqueRoutesMap = {};
			this.filteredPassengers
				?.flatMap((passenger) => passenger?.routes || [])
				.forEach((route) => {
					if (route && route.id) {
						uniqueRoutesMap[route.id] = route;
					}
				});

			return Object.values(uniqueRoutesMap);
		},
		displayedPassengers() {
			if (!this.selectedRouteId) {
				return this.filteredPassengers;
			}

			return this.filteredPassengers.filter((passenger) =>
				passenger.routes?.some((route) => route.id === this.selectedRouteId)
			);
		}
	},
	methods: {
		updatePendingFilters(newFilters) {
			this.pendingFilters = newFilters;
		},
		applyFilters() {
			this.isLoading = true;
			this.activeFilters = { ...this.pendingFilters };
			this.selectedRouteId = null;
			this.selectedRouteName = '';

			setTimeout(() => {
				this.isLoading = false;
			}, 200);
		},
		clearAllFilters() {
			this.pendingFilters = {
				search: '',
				status: null,
				gender: null,
				route_id: null,
				responsible_id: null,
				region_id: null,
				group_id: null
			};
			this.activeFilters = { ...this.pendingFilters };
			this.selectedRouteId = null;
			this.selectedRouteName = '';
			this.applyFilters();
		},
		selectRoute(route) {
			this.isLoading = true;
			this.selectedRouteId = route.id;
			this.selectedRouteName = route.name || `Route #${route.id}`;

			setTimeout(() => {
				this.isLoading = false;
			}, 200);
		},
		clearSelectedRoute() {
			this.isLoading = true;
			this.selectedRouteId = null;
			this.selectedRouteName = '';

			setTimeout(() => {
				this.isLoading = false;
			}, 200);
		}
	}
};
</script>

<style scoped>
.custom-height {
	max-height: calc(100vh - 117px);
}

/* Hide scrollbar while maintaining functionality */
.overflow-auto {
	/* For Chrome, Safari, and Opera */
	&::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For IE and Edge */
	-ms-overflow-style: none;
}
</style>
