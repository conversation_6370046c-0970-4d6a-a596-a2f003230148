<?php

return [
    'driver' => [
        'first_name' => '<PERSON><PERSON>',
        'last_name' => '<PERSON><PERSON>',
        'email_prefix' => 'karim.benali.driver',
        'phone_start' => 600,
    ],
    'plan' => [
        'name' => 'Casa-Plan',
        'status' => 'active',
        'color' => '#4CAF50',
        'description' => 'Casablanca transportation routes plan',
    ],
    'responsible' => [
        'first_name' => '<PERSON>',
        'last_name' => 'Ta<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '212500',
    ],
    'passengers' => [
        [
            'first_name' => 'Youssef',
            'last_name' => '<PERSON> Am<PERSON>',
            'email_prefix' => 'youssef.elamrani',
            'phone_start' => 501,
            'location' => [
                'name' => 'Résidence Anfa 1',
                'lat' => 33.594500,
                'lng' => -7.672000,
            ],
        ],
        [
            'first_name' => '<PERSON><PERSON>',
            'last_name' => 'Benjelloun',
            'email_prefix' => 'salma.benjelloun',
            'phone_start' => 502,
            'location' => [
                'name' => 'Résidence Maarif 2',
                'lat' => 33.586000,
                'lng' => -7.638000,
            ],
        ],
        [
            'first_name' => 'Mehdi',
            'last_name' => 'Ouazzani',
            'email_prefix' => 'mehdi.ouazzani',
            'phone_start' => 503,
            'location' => [
                'name' => 'Résidence Gauthier 3',
                'lat' => 33.593000,
                'lng' => -7.637500,
            ],
        ],
        [
            'first_name' => 'Leila',
            'last_name' => 'Berrada',
            'email_prefix' => 'leila.berrada',
            'phone_start' => 504,
            'location' => [
                'name' => 'Résidence Bourgogne 4',
                'lat' => 33.595000,
                'lng' => -7.642500,
            ],
        ],
        [
            'first_name' => 'Amine',
            'last_name' => 'Chaoui',
            'email_prefix' => 'amine.chaoui',
            'phone_start' => 505,
            'location' => [
                'name' => 'Résidence Racine 5',
                'lat' => 33.588000,
                'lng' => -7.640000,
            ],
        ],
        [
            'first_name' => 'Fatima',
            'last_name' => 'Alaoui',
            'email_prefix' => 'fatima.alaoui',
            'phone_start' => 506,
            'location' => [
                'name' => 'Résidence CIL 6',
                'lat' => 33.582500,
                'lng' => -7.632000,
            ],
        ],
        [
            'first_name' => 'Omar',
            'last_name' => 'Bensouda',
            'email_prefix' => 'omar.bensouda',
            'phone_start' => 507,
            'location' => [
                'name' => 'Résidence Palmier 7',
                'lat' => 33.579500,
                'lng' => -7.635000,
            ],
        ],
        [
            'first_name' => 'Zineb',
            'last_name' => 'Lahlou',
            'email_prefix' => 'zineb.lahlou',
            'phone_start' => 508,
            'location' => [
                'name' => 'Résidence Oasis 8',
                'lat' => 33.576500,
                'lng' => -7.651000,
            ],
        ],
        [
            'first_name' => 'Hamza',
            'last_name' => 'Bennani',
            'email_prefix' => 'hamza.bennani',
            'phone_start' => 509,
            'location' => [
                'name' => 'Résidence Californie 9',
                'lat' => 33.573500,
                'lng' => -7.655000,
            ],
        ],
        [
            'first_name' => 'Amina',
            'last_name' => 'Ziani',
            'email_prefix' => 'amina.ziani',
            'phone_start' => 510,
            'location' => [
                'name' => 'Résidence Sidi Maarouf 10',
                'lat' => 33.570000,
                'lng' => -7.660000,
            ],
        ],
        [
            'first_name' => 'Rachid',
            'last_name' => 'Fassi',
            'email_prefix' => 'rachid.fassi',
            'phone_start' => 511,
            'location' => [
                'name' => 'Résidence Ain Diab 11',
                'lat' => 33.598000,
                'lng' => -7.685000,
            ],
        ],
        [
            'first_name' => 'Nora',
            'last_name' => 'Idrissi',
            'email_prefix' => 'nora.idrissi',
            'phone_start' => 512,
            'location' => [
                'name' => 'Résidence Hay Hassani 12',
                'lat' => 33.568000,
                'lng' => -7.665000,
            ],
        ],
        [
            'first_name' => 'Samir',
            'last_name' => 'Tahiri',
            'email_prefix' => 'samir.tahiri',
            'phone_start' => 513,
            'location' => [
                'name' => 'Résidence Lissasfa 13',
                'lat' => 33.565000,
                'lng' => -7.670000,
            ],
        ],
        [
            'first_name' => 'Houda',
            'last_name' => 'Chraibi',
            'email_prefix' => 'houda.chraibi',
            'phone_start' => 514,
            'location' => [
                'name' => 'Résidence Oulfa 14',
                'lat' => 33.562000,
                'lng' => -7.675000,
            ],
        ],
        [
            'first_name' => 'Kamal',
            'last_name' => 'Benhima',
            'email_prefix' => 'kamal.benhima',
            'phone_start' => 515,
            'location' => [
                'name' => 'Résidence Sidi Moumen 15',
                'lat' => 33.601000,
                'lng' => -7.615000,
            ],
        ],
    ],
    'locations' => [
        // Main Hubs (Origin/Destination points)
        'Gare Casa Voyageurs' => ['lat' => 33.589444, 'lng' => -7.613333],
        'Lycée Mohammed V' => ['lat' => 33.592778, 'lng' => -7.618889],
        'Morocco Mall' => ['lat' => 33.578333, 'lng' => -7.705000],

        // North Route (Downtown/Business District)
        'Place Mohammed V' => ['lat' => 33.593611, 'lng' => -7.618056],
        'Marché Central' => ['lat' => 33.596389, 'lng' => -7.619444],
        'Mosquée Hassan II' => ['lat' => 33.608333, 'lng' => -7.632778],
        'Marina Casablanca' => ['lat' => 33.607222, 'lng' => -7.631111],
        'Quartier des Habous' => ['lat' => 33.584722, 'lng' => -7.621111],

        // East Route (Maarif/Gauthier Area)
        'Twin Center' => ['lat' => 33.593056, 'lng' => -7.636389],
        'Quartier Gauthier' => ['lat' => 33.592500, 'lng' => -7.638056],
        'Maarif Centre' => ['lat' => 33.584167, 'lng' => -7.636389],
        'Parc de la Ligue Arabe' => ['lat' => 33.593889, 'lng' => -7.641667],
        'Derb Ghallef' => ['lat' => 33.581111, 'lng' => -7.630556],

        // West Route (Ain Diab/Anfa Area)
        'Anfa Place' => ['lat' => 33.595000, 'lng' => -7.670833],
        'Corniche Ain Diab' => ['lat' => 33.598333, 'lng' => -7.688889],
        'Royal Golf Anfa' => ['lat' => 33.589167, 'lng' => -7.673056],
        'Technopark' => ['lat' => 33.581944, 'lng' => -7.653889],
        'Faculté des Sciences' => ['lat' => 33.565833, 'lng' => -7.658056],

        // California Small
        'Écoles Nour Californie' => ['lat' => 33.539480529256466, 'lng' => -7.615497235940381],
        'Californie Stop 1' => ['lat' => 33.5392203, 'lng' => -7.6157207],
        'Californie Stop 2' => ['lat' => 33.53906002759861, 'lng' => -7.616033764131463],
        'Californie Stop 3' => ['lat' => 33.538996618439086, 'lng' => -7.616510233198862],
        'Californie Stop 4' => ['lat' => 33.5382495, 'lng' => -7.6166589],
        'Californie Stop 5' => ['lat' => 33.5378936, 'lng' => -7.6160508],

        // Polo Long
        'Écoles Al Madina Site Polo' => ['lat' => 33.555431753442285, 'lng' => -7.610777433151352],
        'Polo Stop 1' => ['lat' => 33.55410601951115, 'lng' => -7.6127512641571435],
        'Polo Stop 2' => ['lat' => 33.55399931608277, 'lng' => -7.6123203966780295],
        'Polo Stop 3' => ['lat' => 33.553564751391434, 'lng' => -7.612167556026874],
        'Polo Stop 4' => ['lat' => 33.55313393078958, 'lng' => -7.612185537279951],
        'Polo Stop 5' => ['lat' => 33.55239823133427, 'lng' => -7.612061034934177],
        'Polo Stop 6' => ['lat' => 33.55201535502568, 'lng' => -7.612003683306919],
        'Polo Stop 7' => ['lat' => 33.55186389488465, 'lng' => -7.611506059488212],
        'Polo Stop 8' => ['lat' => 33.5517304654928, 'lng' => -7.61195608450687],
        'Polo Stop 9' => ['lat' => 33.55159754717477, 'lng' => -7.612733235723462],
        'Polo Stop 10' => ['lat' => 33.551751703140624, 'lng' => -7.613415616356638],
        'Polo Stop 11' => ['lat' => 33.55155406104936, 'lng' => -7.6142153250111395],
        'Polo Stop 12' => ['lat' => 33.5518849963832, 'lng' => -7.613856834928532],
        'Polo Stop 13' => ['lat' => 33.55251928556858, 'lng' => -7.613481799148737],
        'Polo Stop 14' => ['lat' => 33.55321791754298, 'lng' => -7.614452479986926],
        'Polo Stop 15' => ['lat' => 33.55351421930098, 'lng' => -7.614994617891949],
        'Polo Stop 16' => ['lat' => 33.55298756177746, 'lng' => -7.615550877331716],
        'Polo Stop 17' => ['lat' => 33.55200156193203, 'lng' => -7.616498241762914],
        'Polo Stop 18' => ['lat' => 33.55117988680509, 'lng' => -7.615855234235419],
        'Polo Stop 19' => ['lat' => 33.55089408492684, 'lng' => -7.6148692893548],
        'Polo Stop 20' => ['lat' => 33.551029545655474, 'lng' => -7.613568167229192],
        'Polo Stop 21' => ['lat' => 33.551217499210296, 'lng' => -7.611272254717834],
        'Polo Stop 22' => ['lat' => 33.550807582949986, 'lng' => -7.6090848890760014],
        'Polo Stop 23' => ['lat' => 33.551663624293916, 'lng' => -7.60790135077615],
        'Polo Stop 24' => ['lat' => 33.55322617953461, 'lng' => -7.606528191531099],
        'Polo Stop 25' => ['lat' => 33.55419118913154, 'lng' => -7.60758730424869],
        'Polo Stop 26' => ['lat' => 33.55485413662175, 'lng' => -7.607883855809234],
        'Polo Stop 27' => ['lat' => 33.55525425632127, 'lng' => -7.606965958119937],
        'Polo Stop 28' => ['lat' => 33.5559033946497, 'lng' => -7.606342546837763],
        'Polo Stop 29' => ['lat' => 33.55623361489095, 'lng' => -7.606929314379316],
        'Polo Stop 30' => ['lat' => 33.55488414920483, 'lng' => -7.609573578512611],

    ],

    'routes' => [
        [
            'name' => 'Casa California 1',
            'origin' => 'Écoles Nour Californie',
            'destination' => 'Écoles Nour Californie',
            'stops' => [
                'Californie Stop 1',
                'Californie Stop 2',
                'Californie Stop 3',
                'Californie Stop 4',
                'Californie Stop 5',
            ],
        ],
        [
            'name' => 'Casa Polo 1',
            'origin' => 'Écoles Al Madina Site Polo',
            'destination' => 'Écoles Al Madina Site Polo',
            'stops' => [
                'Polo Stop 1',
                'Polo Stop 2',
                'Polo Stop 3',
                'Polo Stop 4',
                'Polo Stop 5',
                'Polo Stop 6',
                'Polo Stop 7',
                'Polo Stop 8',
                'Polo Stop 9',
                'Polo Stop 10',
                'Polo Stop 11',
                'Polo Stop 12',
                'Polo Stop 13',
                'Polo Stop 14',
                'Polo Stop 15',
                'Polo Stop 16',
                'Polo Stop 17',
                'Polo Stop 18',
                'Polo Stop 19',
                'Polo Stop 20',
                'Polo Stop 21',
                'Polo Stop 22',
                'Polo Stop 23',
                'Polo Stop 24',
                'Polo Stop 25',
                'Polo Stop 26',
                'Polo Stop 27',
                'Polo Stop 28',
                'Polo Stop 29',
                'Polo Stop 30',
            ],
        ],
        [
            'name' => 'Casa-West',
            'origin' => 'Lycée Mohammed V',
            'destination' => 'Morocco Mall',
            'stops' => [
                'Anfa Place',
                'Royal Golf Anfa',
                'Technopark',
                'Faculté des Sciences',
            ],
        ],
    ],

    'schedules' => [
        [
            'name' => 'Casa-S1',
            'start_time' => '07:15:00',
            'arrival_time' => '08:00:00',
            'start_date' => '2024-09-01',
            'end_date' => '2025-06-30',
            'route_name' => 'Casa California 1',
            'color' => '#4CAF50', // Green
        ],
        [
            'name' => 'Casa-S2',
            'start_time' => '13:30:00',
            'arrival_time' => '14:15:00',
            'start_date' => '2024-09-01',
            'end_date' => '2025-06-30',
            'route_name' => 'Casa Polo 1',
            'color' => '#2196F3', // Blue
        ],
        [
            'name' => 'Casa-S3',
            'start_time' => '16:45:00',
            'arrival_time' => '17:30:00',
            'start_date' => '2024-09-01',
            'end_date' => '2025-06-30',
            'route_name' => 'Casa-West',
            'color' => '#FF9800', // Orange
        ],
    ],
];
