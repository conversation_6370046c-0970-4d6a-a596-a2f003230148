name: Deploy

on:
    push:
        branches: [production]

jobs:
    deploy:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: 8.3
                  tools: composer:v2
                  coverage: none
            - name: Require Vapor CLI
              run: composer global require laravel/vapor-cli
            - name: Install Project Dependencies
              run: composer install --no-interaction --prefer-dist --optimize-autoloader
            - name: Build Frontend Assets
              run: npm run build
            - name: Deploy Environment
              run: vapor deploy production --commit="${{ github.sha }}" --message="${{ github.event.head_commit.message }}"
              env:
                  VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}
