name: <PERSON><PERSON> Tests

on:
    push:
        branches: [develop]
    pull_request:
        branches: [develop]

jobs:
    laravel-tests:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '8.4'
                  extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
                  tools: composer

            - name: Install Composer Dependencies
              run: composer install --prefer-dist --no-progress --no-scripts

            - name: Generate Laravel Key
              run: php artisan key:generate

            - name: Configure Environment
              run: |
                  cp .env.example .env
                  echo "APP_ENV=testing" >> .env
                  echo "DB_CONNECTION=sqlite" >> .env
                  echo "DB_DATABASE=:memory:" >> .env

            - name: Run Migrations
              run: php artisan migrate --no-interaction

            - name: Run Tests
              env:
                  APP_ENV: testing
              run: php artisan test --parallel
