<?php

return [
    'transport' => [
        'ride' => [
            'started' => [
                'responsible' => [
                    'title' => 'Ride Started',
                    'body' => ':ride_mode ride has started for :names.',
                ],
                'passenger' => [
                    'title' => 'Ride Started',
                    'body' => 'Your :ride_mode has started. Please be ready.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => 'Ride Canceled',
                    'body' => ':ride_mode has been canceled for :names.',
                ],
                'passenger' => [
                    'title' => 'Ride Canceled',
                    'body' => 'Your :ride_mode has been canceled.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Ride Arrived',
                    'body' => ':ride_mode has arrived at its destination for :names.',
                ],
                'passenger' => [
                    'title' => 'Ride Arrived',
                    'body' => 'You have arrived at your destination.',
                ],
            ],
            'upcoming' => [
                'responsible' => [
                    'title' => 'Upcoming Ride',
                    'body' => ':ride_mode is scheduled at :time for :names.',
                ],
                'passenger' => [
                    'title' => 'Upcoming Ride',
                    'body' => 'Your :ride_mode is scheduled at :time.',
                ],
            ],
        ],
        'stop' => [
            'scheduled' => [
                'responsible' => [
                    'title' => ':ride_mode scheduled',
                    'body' => ':ride_mode is scheduled at :time. Click to view the bus\'s live location for :names.',
                ],
                'passenger' => [
                    'title' => ':ride_mode scheduled',
                    'body' => 'Your :ride_mode is scheduled at :time. Click to view the bus\'s live location.',
                ],
            ],
            'arrived' => [
                'responsible' => [
                    'title' => 'Bus Arrived',
                    'body' => 'The bus has arrived at the stop for :ride_mode for :names.',
                ],
                'passenger' => [
                    'title' => 'Bus Arrived',
                    'body' => 'The bus has arrived at your stop.',
                ],
            ],
            'canceled' => [
                'responsible' => [
                    'title' => ':ride_mode canceled',
                    'body' => ':ride_mode has been canceled for :names.',
                ],
                'passenger' => [
                    'title' => ':ride_mode canceled',
                    'body' => 'Your :ride_mode has been canceled.',
                ],
            ],
            'departed' => [
                'responsible' => [
                    'title' => 'Bus departed',
                    'body' => 'The bus has departed from the stop for :ride_mode for :names.',
                ],
                'passenger' => [
                    'title' => 'Bus departed',
                    'body' => 'The bus has departed from your stop.',
                ],
            ],
            'delayed' => [
                'responsible' => [
                    'title' => ':ride_mode delayed',
                    'body' => 'The bus is delayed for :ride_mode, expected arrival around :time for :names.',
                ],
                'passenger' => [
                    'title' => ':ride_mode delayed',
                    'body' => 'The bus is delayed, expected arrival around :time.',
                ],
            ],
            'skipped' => [
                'responsible' => [
                    'title' => ':ride_mode skipped',
                    'body' => ':ride_mode has been skipped for :names.',
                ],
                'passenger' => [
                    'title' => ':ride_mode skipped',
                    'body' => 'Your :ride_mode has been skipped. Please check your itinerary for updates.',
                ],
            ],
            'ignored' => [
                'responsible' => [
                    'title' => ':ride_mode ignored',
                    'body' => ':ride_mode has been ignored for :names.',
                ],
                'passenger' => [
                    'title' => ':ride_mode ignored',
                    'body' => 'Your :ride_mode has been ignored. Please check your itinerary for updates.',
                ],
            ],
            'no_show' => [
                'responsible' => [
                    'title' => ':names did not show up for :ride_mode',
                    'body' => ':names did not board the :ride_mode at the scheduled stop. Please check for any issues or contact them.',
                ],
                'passenger' => [
                    'title' => 'You missed your :ride_mode',
                    'body' => 'You did not board your :ride_mode at the scheduled stop. Please review your itinerary and contact support if needed.',
                ],
            ],
        ],
    ],
    'incident' => [
        'mechanical_issue' => [
            'title' => 'Mechanical Issue Reported',
            'body' => 'A mechanical issue has been reported at :time. Please investigate and address the problem promptly.',
        ],
        'accident' => [
            'title' => 'Accident Reported',
            'body' => 'An accident has occurred at :time. Please assess the situation and provide further instructions to the involved parties.',
        ],
        'health_emergency' => [
            'title' => 'Health Emergency Reported',
            'body' => 'A health emergency has been reported at :time. Please ensure that emergency services are notified and assist as needed.',
        ],
        'behavioral_issue' => [
            'title' => 'Behavioral Issue Reported',
            'body' => 'A behavioral issue has been reported at :time. Please address the situation and ensure the safety of all passengers.',
        ],
        'traffic_delay' => [
            'title' => 'Traffic Delay Alert',
            'body' => 'A traffic delay is affecting rides at :time. Please monitor the situation and adjust routes as necessary.',
        ],
        'weather_condition' => [
            'title' => 'Weather Condition Alert',
            'body' => 'Adverse weather conditions are impacting rides at :time. Please prepare for potential delays and communicate with drivers.',
        ],
        'road_blockage' => [
            'title' => 'Road Blockage Reported',
            'body' => 'A road blockage has been reported at :time. Please find an alternate route and inform drivers accordingly.',
        ],
        'missed_station' => [
            'title' => 'Missed Station Alert',
            'body' => 'A station has been missed at :time. Please review the itinerary and notify affected passengers.',
        ],
        'other' => [
            'title' => 'Other Incident Reported',
            'body' => 'An incident has occurred at :time. Please investigate and provide updates as necessary.',
        ],
    ],
    'auth' => [
        'otp' => [
            'title' => 'OTP Code',
            'body' => 'Your OTP code is :otp.',
        ],
    ],
];
