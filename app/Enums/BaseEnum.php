<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

class BaseEnum extends Enum
{
    public static function asOptions(): array
    {
        $options = [];
        foreach (self::asArray() as $key => $value) {
            $options[] = [
                'id' => $value,
                'key' => $key,
                'name' => __('general.enums.'.$value),
            ];
        }

        return $options;
    }

    public static function get(): array
    {
        $options = [];
        foreach (self::asArray() as $key => $value) {
            $options[$key] = $value;
        }

        return $options;
    }
}
