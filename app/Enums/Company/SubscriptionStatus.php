<?php

declare(strict_types=1);

namespace App\Enums\Company;

use App\Enums\BaseEnum;

/**
 * @method static static Text()
 */
final class SubscriptionStatus extends BaseEnum
{
    public const string Active = 'active';

    public const string Inactive = 'inactive';

    public static function styles(): array
    {
        return [
            self::Active => 'success',
            self::Inactive => 'danger',
        ];
    }
}
