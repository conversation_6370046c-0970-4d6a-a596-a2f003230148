<?php

declare(strict_types=1);

namespace App\Enums\Import;

use App\Enums\BaseEnum;

final class ImportSection extends BaseEnum
{
    public const string Transport = 'transport';

    public static function list(): array
    {
        return [
            self::Transport => [
                [
                    'id' => ImportType::Passengers,
                    'name' => __('import.types.'.ImportType::Passengers),
                ],
                [
                    'id' => ImportType::Responsibles,
                    'name' => __('import.types.'.ImportType::Responsibles),
                ],
            ],
        ];
    }
}
