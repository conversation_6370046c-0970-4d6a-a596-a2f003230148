<?php

namespace App\Enums\System;

use App\Enums\BaseEnum;

class UserEvent extends BaseEnum
{
    // Transport
    // ---- Ride
    public const RideStarted = 'RideStarted';

    public const RideArrived = 'RideArrived';

    public const RideCanceled = 'RideCanceled';

    // ---- Stop
    public const StopScheduled = 'StopScheduled';

    public const StopArrived = 'StopArrived';

    public const StopDeparted = 'StopDeparted';

    public const StopDelayed = 'StopDelayed';

    public const StopSkipped = 'StopSkipped';

    // Auth
    public const OtpSent = 'OtpSent';

    public const OtpVerify = 'OtpVerify';

    public const UserLogin = 'UserLogin';

    public const UserLogout = 'UserLogout';

    // Route Optimisation
    public const RouteOptimized = 'RouteOptimized';

    // SOS
    public const IncidentReported = 'IncidentReported';

    const RideRequested = 'RideRequested';

    // Geo
    const LocationCreated = 'LocationCreated';

    const LocationDeleted = 'LocationDeleted';

    const LocationUpdated = 'LocationUpdated';

    const ViewLiveMap = 'ViewLiveMap';

    // Plan

    const AbsenceCreated = 'AbsenceCreated';

    const AbsenceDeleted = 'AbsenceDeleted';

    const AttendanceCreated = 'AttendanceCreated';

    const AttendanceDeleted = 'AttendanceDeleted';

    // Review
    const ReviewCreated = 'ReviewCreated';
}
