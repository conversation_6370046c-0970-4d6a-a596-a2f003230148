<?php

namespace App\Enums\General;

use App\Enums\BaseEnum;
use App\Models\Auth\User;
use App\Models\Company\Company;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Fleet\Vehicle;
use App\Models\General\Media;
use App\Models\General\Region;
use App\Models\Geo\Location;
use App\Models\Plan\Plan;
use App\Models\Plan\Schedule;
use App\Models\Transport\Group;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Models\Transport\Ride;
use App\Models\Transport\Route;
use App\Models\Transport\Station;

/**
 * @method static ModelType User()
 * @method static ModelType Country()
 */
class ModelType extends BaseEnum
{
    public const string User = User::class;

    public const string Assistant = Assistant::class;

    public const string Company = Company::class;

    public const string Location = Location::class;

    public const string Media = Media::class;

    public const string Route = Route::class;

    public const string Station = Station::class;

    public const string Region = Region::class;

    public const string Vehicle = Vehicle::class;

    public const string Passenger = Passenger::class;

    public const string Driver = Driver::class;

    public const string Ride = Ride::class;

    public const string Plan = Plan::class;

    public const string Schedule = Schedule::class;

    public const string Responsible = Responsible::class;

    public const string Group = Group::class;
}
