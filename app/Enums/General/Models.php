<?php

namespace App\Enums\General;

use App\Enums\BaseEnum;

class Models extends BaseEnum
{
    public const string User = 'User';

    public const string Driver = 'Driver';

    public const string Assistant = 'Assistant';

    public const string Vehicle = 'Vehicle';

    public const string Schedule = 'Schedule';

    public const string Passenger = 'Passenger';

    public const string Responsible = 'Responsible';

    public const string Company = 'Company';

    public const string Reason = 'Reason';

    public const string Region = 'Region';

    public const string Location = 'Location';

    public const string Plan = 'Plan';

    public const string Attendance = 'Attendance';

    public const string Route = 'Route';

    public const string Station = 'Station';

    public const string Ride = 'Ride';

    public const string Group = 'Group';

    public const string Stop = 'Stop';

    public const string StationPassenger = 'StationPassenger';

    public const string PassengerResponsible = 'PassengerResponsible';
}
