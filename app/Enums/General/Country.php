<?php

namespace App\Enums\General;

use App\Enums\BaseEnum;

final class Country extends BaseEnum
{
    public const AF = 'Afghanistan';

    public const AL = 'Albania';

    public const DZ = 'Algeria';

    public const AS = 'American Samoa';

    public const AD = 'Andorra';

    public const AO = 'Angola';

    public const AI = 'Anguilla';

    public const AQ = 'Antarctica';

    public const AG = 'Antigua and Barbuda';

    public const AR = 'Argentina';

    public const AM = 'Armenia';

    public const AW = 'Aruba';

    public const AU = 'Australia';

    public const AT = 'Austria';

    public const AZ = 'Azerbaijan';

    public const BS = 'Bahamas';

    public const BH = 'Bahrain';

    public const BD = 'Bangladesh';

    public const BB = 'Barbados';

    public const BY = 'Belarus';

    public const BE = 'Belgium';

    public const BZ = 'Belize';

    public const BJ = 'Benin';

    public const BM = 'Bermuda';

    public const BT = 'Bhutan';

    public const BO = 'Bolivia';

    public const BA = 'Bosnia and Herzegovina';

    public const BW = 'Botswana';

    public const BV = 'Bouvet Island';

    public const BR = 'Brazil';

    public const IO = 'British Indian Ocean Territory';

    public const BN = 'Brunei Darussalam';

    public const BG = 'Bulgaria';

    public const BF = 'Burkina Faso';

    public const BI = 'Burundi';

    public const KH = 'Cambodia';

    public const CM = 'Cameroon';

    public const CA = 'Canada';

    public const CV = 'Cape Verde';

    public const KY = 'Cayman Islands';

    public const CF = 'Central African Republic';

    public const TD = 'Chad';

    public const CL = 'Chile';

    public const CN = 'China';

    public const CX = 'Christmas Island';

    public const CC = 'Cocos (Keeling) Islands';

    public const CO = 'Colombia';

    public const KM = 'Comoros';

    public const CG = 'Congo';

    public const CD = 'Congo, the Democratic Republic of the';

    public const CK = 'Cook Islands';

    public const CR = 'Costa Rica';

    public const CI = "Cote D'Ivoire";

    public const HR = 'Croatia';

    public const CU = 'Cuba';

    public const CY = 'Cyprus';

    public const CZ = 'Czech Republic';

    public const DK = 'Denmark';

    public const DJ = 'Djibouti';

    public const DM = 'Dominica';

    public const DO = 'Dominican Republic';

    public const EC = 'Ecuador';

    public const EG = 'Egypt';

    public const SV = 'El Salvador';

    public const GQ = 'Equatorial Guinea';

    public const ER = 'Eritrea';

    public const EE = 'Estonia';

    public const ET = 'Ethiopia';

    public const FK = 'Falkland Islands (Malvinas)';

    public const FO = 'Faroe Islands';

    public const FJ = 'Fiji';

    public const FI = 'Finland';

    public const FR = 'France';

    public const GF = 'French Guiana';

    public const PF = 'French Polynesia';

    public const TF = 'French Southern Territories';

    public const GA = 'Gabon';

    public const GM = 'Gambia';

    public const GE = 'Georgia';

    public const DE = 'Germany';

    public const GH = 'Ghana';

    public const GI = 'Gibraltar';

    public const GR = 'Greece';

    public const GL = 'Greenland';

    public const GD = 'Grenada';

    public const GP = 'Guadeloupe';

    public const GU = 'Guam';

    public const GT = 'Guatemala';

    public const GN = 'Guinea';

    public const GW = 'Guinea-Bissau';

    public const GY = 'Guyana';

    public const HT = 'Haiti';

    public const HM = 'Heard Island and Mcdonald Islands';

    public const VA = 'Holy See (Vatican City State)';

    public const HN = 'Honduras';

    public const HK = 'Hong Kong';

    public const HU = 'Hungary';

    public const IS = 'Iceland';

    public const IN = 'India';

    public const ID = 'Indonesia';

    public const IR = 'Iran, Islamic Republic of';

    public const IQ = 'Iraq';

    public const IE = 'Ireland';

    public const IL = 'Israel';

    public const IT = 'Italy';

    public const JM = 'Jamaica';

    public const JP = 'Japan';

    public const JO = 'Jordan';

    public const KZ = 'Kazakhstan';

    public const KE = 'Kenya';

    public const KI = 'Kiribati';

    public const KP = "Korea, Democratic People's Republic of";

    public const KR = 'Korea, Republic of';

    public const KW = 'Kuwait';

    public const KG = 'Kyrgyzstan';

    public const LA = "Lao People's Democratic Republic";

    public const LV = 'Latvia';

    public const LB = 'Lebanon';

    public const LS = 'Lesotho';

    public const LR = 'Liberia';

    public const LY = 'Libyan Arab Jamahiriya';

    public const LI = 'Liechtenstein';

    public const LT = 'Lithuania';

    public const LU = 'Luxembourg';

    public const MO = 'Macao';

    public const MK = 'Macedonia, the Former Yugoslav Republic of';

    public const MG = 'Madagascar';

    public const MW = 'Malawi';

    public const MY = 'Malaysia';

    public const MV = 'Maldives';

    public const ML = 'Mali';

    public const MT = 'Malta';

    public const MH = 'Marshall Islands';

    public const MQ = 'Martinique';

    public const MR = 'Mauritania';

    public const MU = 'Mauritius';

    public const YT = 'Mayotte';

    public const MX = 'Mexico';

    public const FM = 'Micronesia, Federated States of';

    public const MD = 'Moldova, Republic of';

    public const MC = 'Monaco';

    public const MN = 'Mongolia';

    public const MS = 'Montserrat';

    public const MA = 'Morocco';

    public const MZ = 'Mozambique';

    public const MM = 'Myanmar';

    public const NA = 'Namibia';

    public const NR = 'Nauru';

    public const NP = 'Nepal';

    public const NL = 'Netherlands';

    public const AN = 'Netherlands Antilles';

    public const NC = 'New Caledonia';

    public const NZ = 'New Zealand';

    public const NI = 'Nicaragua';

    public const NE = 'Niger';

    public const NG = 'Nigeria';

    public const NU = 'Niue';

    public const NF = 'Norfolk Island';

    public const MP = 'Northern Mariana Islands';

    public const NO = 'Norway';

    public const OM = 'Oman';

    public const PK = 'Pakistan';

    public const PW = 'Palau';

    public const PS = 'Palestinian Territory, Occupied';

    public const PA = 'Panama';

    public const PG = 'Papua New Guinea';

    public const PY = 'Paraguay';

    public const PE = 'Peru';

    public const PH = 'Philippines';

    public const PN = 'Pitcairn';

    public const PL = 'Poland';

    public const PT = 'Portugal';

    public const PR = 'Puerto Rico';

    public const QA = 'Qatar';

    public const RE = 'Reunion';

    public const RO = 'Romania';

    public const RU = 'Russian Federation';

    public const RW = 'Rwanda';

    public const SH = 'Saint Helena';

    public const KN = 'Saint Kitts and Nevis';

    public const LC = 'Saint Lucia';

    public const PM = 'Saint Pierre and Miquelon';

    public const VC = 'Saint Vincent and the Grenadines';

    public const WS = 'Samoa';

    public const SM = 'San Marino';

    public const ST = 'Sao Tome and Principe';

    public const SA = 'Saudi Arabia';

    public const SN = 'Senegal';

    public const CS = 'Serbia and Montenegro';

    public const SC = 'Seychelles';

    public const SL = 'Sierra Leone';

    public const SG = 'Singapore';

    public const SK = 'Slovakia';

    public const SI = 'Slovenia';

    public const SB = 'Solomon Islands';

    public const SO = 'Somalia';

    public const ZA = 'South Africa';

    public const GS = 'South Georgia and the South Sandwich Islands';

    public const ES = 'Spain';

    public const LK = 'Sri Lanka';

    public const SD = 'Sudan';

    public const SR = 'Suriname';

    public const SJ = 'Svalbard and Jan Mayen';

    public const SZ = 'Swaziland';

    public const SE = 'Sweden';

    public const CH = 'Switzerland';

    public const SY = 'Syrian Arab Republic';

    public const TW = 'Taiwan, Province of China';

    public const TJ = 'Tajikistan';

    public const TZ = 'Tanzania, United Republic of';

    public const TH = 'Thailand';

    public const TL = 'Timor-Leste';

    public const TG = 'Togo';

    public const TK = 'Tokelau';

    public const TO = 'Tonga';

    public const TT = 'Trinidad and Tobago';

    public const TN = 'Tunisia';

    public const TR = 'Turkey';

    public const TM = 'Turkmenistan';

    public const TC = 'Turks and Caicos Islands';

    public const TV = 'Tuvalu';

    public const UG = 'Uganda';

    public const UA = 'Ukraine';

    public const AE = 'United Arab Emirates';

    public const GB = 'United Kingdom';

    public const US = 'United States';

    public const UM = 'United States Minor Outlying Islands';

    public const UY = 'Uruguay';

    public const UZ = 'Uzbekistan';

    public const VU = 'Vanuatu';

    public const VE = 'Venezuela';

    public const VN = 'Viet Nam';

    public const VG = 'Virgin Islands, British';

    public const VI = 'Virgin Islands, U.s.';

    public const WF = 'Wallis and Futuna';

    public const EH = 'Western Sahara';

    public const YE = 'Yemen';

    public const ZM = 'Zambia';

    public const ZW = 'Zimbabwe';

    public static function asOptions(): array
    {
        $options = [];
        foreach (self::asArray() as $key => $value) {
            $options[] = [
                'id' => $value,
                'key' => $key,
                'name' => __('country.'.$key),
            ];
        }

        return $options;
    }
}
