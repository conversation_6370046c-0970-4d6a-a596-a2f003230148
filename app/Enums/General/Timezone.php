<?php

namespace App\Enums\General;

use App\Enums\BaseEnum;

class Timezone extends BaseEnum
{
    public static function asOptions(): array
    {
        return collect(timezone_identifiers_list())->map(function ($value) {
            return [
                'id' => $value,
                'key' => $value,
                'name' => str_replace('_', ' ', $value),
            ];
        })->toArray();
    }
}
