<?php

namespace App\Enums\General;

use App\Enums\Auth\UserGender;
use App\Enums\Auth\UserStatus;
use App\Enums\BaseEnum;
use App\Enums\Company\Industry;
use App\Enums\Fleet\AssistantStatus;
use App\Enums\Fleet\DriverStatus;
use App\Enums\Fleet\VehicleStatus;
use App\Enums\Fleet\VehicleType;
use App\Enums\Import\ImportType;
use App\Enums\Incident\IncidentType;
use App\Enums\Plan\AbsenceFor;
use App\Enums\Plan\AbsenceReason;
use App\Enums\Plan\AttendanceStatus;
use App\Enums\Plan\PlanStatus;
use App\Enums\Transport\PassengerStatus;
use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use App\Enums\Transport\RouteStatus;

class EnumType extends BaseEnum
{
    public const string UserStatus = UserStatus::class;

    public const string VehicleStatus = VehicleStatus::class;

    public const string VehicleType = VehicleType::class;

    public const string RideStatus = RideStatus::class;

    public const string RideMode = RideMode::class;

    public const string PlanStatus = PlanStatus::class;

    public const string PassengerStatus = PassengerStatus::class;

    public const string DriverStatus = DriverStatus::class;

    public const string UserGender = UserGender::class;

    public const string AssistantStatus = AssistantStatus::class;

    public const string IncidentType = IncidentType::class;

    public const string Industry = Industry::class;

    public const string ImportType = ImportType::class;

    public const string RouteStatus = RouteStatus::class;

    public const string Timezone = Timezone::class;

    public const string Country = Country::class;

    public const string AttendanceStatus = AttendanceStatus::class;

    public const string AbsenceFor = AbsenceFor::class;

    public const string AbsenceReason = AbsenceReason::class;
}
