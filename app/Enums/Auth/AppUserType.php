<?php

declare(strict_types=1);

namespace App\Enums\Auth;

use App\Enums\BaseEnum;
use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;

/**
 * @method static static Text()
 */
class AppUserType extends BaseEnum
{
    public const string Passenger = Passenger::class;

    public const string User = User::class;

    public const string Driver = Driver::class;

    public const string Responsible = Responsible::class;

    public const string Assistant = Assistant::class;
}
