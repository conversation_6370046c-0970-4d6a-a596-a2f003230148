<?php

declare(strict_types=1);

namespace App\Enums\Notification;

use App\Enums\BaseEnum;

final class NotificationType extends BaseEnum
{
    public const string RideStarted = 'transport.ride.started';

    public const string RideCanceled = 'transport.ride.canceled';

    public const string RideArrived = 'transport.ride.arrived';

    public const string StopScheduled = 'transport.stop.scheduled';

    public const string StopArrived = 'transport.stop.arrived';

    public const string StopCanceled = 'transport.stop.canceled';

    public const string StopDeparted = 'transport.stop.departed';

    public const string StopNoShow = 'transport.stop.no_show';

    public const string StopDelayed = 'transport.stop.delayed';

    public const string StopSkipped = 'transport.stop.skipped';

    public const string StopIgnored = 'transport.stop.ignored';

    public const string UpcomingRide = 'plan.ride.upcoming';

    public const string NewIncident = 'incident';

    public const string VerifyOtp = 'auth.otp';
}
