<?php

namespace App\Enums\Transport;

use App\Enums\BaseEnum;

class RideMode extends BaseEnum
{
    public const string PickUp = 'pickup';

    public const string DropOff = 'drop_off';

    // in the future it might be mode per passenger for the same ride

    public static function colors(): array
    {
        return [
            self::PickUp => 'blue',
            self::DropOff => 'green',
        ];
    }
}
