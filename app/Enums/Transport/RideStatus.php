<?php

namespace App\Enums\Transport;

use App\Enums\BaseEnum;

class RideStatus extends BaseEnum
{
    public const string Ongoing = 'ongoing';

    public const string Canceled = 'canceled';

    public const string Arrived = 'arrived';

    public const string Stalled = 'stalled';

    public static function colors(): array
    {
        return [
            self::Ongoing => 'info',
            self::Canceled => 'danger',
            self::Arrived => 'success',
            self::Stalled => 'warning',
        ];
    }
}
