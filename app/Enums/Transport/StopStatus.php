<?php

namespace App\Enums\Transport;

use App\Enums\BaseEnum;

class StopStatus extends BaseEnum
{
    public const string Scheduled = 'scheduled';

    public const string Arrived = 'arrived';

    public const string Skipped = 'skipped';

    public const string Departed = 'departed';

    public const string Canceled = 'canceled';

    public static function getFinished(): array
    {
        return [
            StopStatus::Skipped,
            StopStatus::Arrived,
            StopStatus::Departed,
        ];
    }
}
