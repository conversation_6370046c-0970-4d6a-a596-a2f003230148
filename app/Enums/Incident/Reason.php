<?php

declare(strict_types=1);

namespace App\Enums\Incident;

use App\Enums\BaseEnum;

final class Reason extends BaseEnum
{
    public const string TRAFFIC_JAM = 'traffic_jam';

    public const string VEHICLE_BREAKDOWN = 'vehicle_breakdown';

    public const string DRIVER_ILLNESS = 'driver_illness';

    public const string WEATHER_CONDITION = 'weather_condition';

    public const string ACCIDENT_ON_ROUTE = 'accident_on_route';

    public const string ROAD_BLOCKAGE = 'road_blockage';

    public const string PASSENGER_ISSUE = 'passenger_issue';

    public const string ADMINISTRATIVE_DELAY = 'administrative_delay';

    public const string OTHER = 'other';
}
