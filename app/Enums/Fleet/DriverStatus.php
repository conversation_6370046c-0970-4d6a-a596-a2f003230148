<?php

declare(strict_types=1);

namespace App\Enums\Fleet;

use App\Enums\BaseEnum;

/**
 * @method static static Text()
 */
final class DriverStatus extends BaseEnum
{
    public const string Active = 'active';

    public const string Inactive = 'inactive';

    public static function styles(): array
    {
        return [
            self::Active => 'success',
            self::Inactive => 'danger',
        ];
    }
}
