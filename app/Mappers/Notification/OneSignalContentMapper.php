<?php

namespace App\Mappers\Notification;

use App\Enums\General\Locale;
use App\Models\Transport\Passenger;
use App\Support\NotificationMessage;
use Illuminate\Support\Collection;

class OneSignalContentMapper
{
    public function getExternalIdsFor(string $role, Collection $passengerIds): Collection
    {
        return $passengerIds->map(fn ($passengerId) => Passenger::getOneSignal($role, $passengerId));
    }

    public function getHeadings(NotificationMessage $notificationMessage): array
    {
        return [
            Locale::English => $notificationMessage->getTitle(Locale::English),
            Locale::Arabic => $notificationMessage->getTitle(Locale::Arabic),
            Locale::French => $notificationMessage->getTitle(Locale::French),
        ];
    }

    public function getContents(NotificationMessage $notificationMessage): array
    {
        return [
            Locale::English => $notificationMessage->getBody(Locale::English),
            Locale::Arabic => $notificationMessage->getBody(Locale::Arabic),
            Locale::French => $notificationMessage->getBody(Locale::French),
        ];
    }
}
