<?php

namespace App\Filament\Pages;

use App\Filament\Resources\CompanyResource\Widgets\CompanyOverview;
use App\Filament\Widgets\StopChartWidget;
use App\Models\Company\Company;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Dashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;

class CompanyPage extends Dashboard
{
    use HasFiltersForm;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $title = 'Company';

    protected static ?string $slug = 'company';

    protected function getFooterWidgets(): array
    {
        return [
            CompanyOverview::class,
            StopChartWidget::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function filtersForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        DatePicker::make('start_date')
                            ->label('Start Date'),
                        DatePicker::make('end_date')
                            ->label('End Date'),
                        Select::make('company_id')
                            ->label('Company')
                            ->options(Company::pluck('name', 'id')->toArray()),
                    ])
                    ->columns(3),
            ]);
    }
}
