<?php

namespace App\Filament\Pages;

use App\Models\Company\Company;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Navigation\NavigationItem;
use Filament\Pages\Page;
use Illuminate\Support\Str;

class CompanySelector extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $title = 'Select Company';

    protected static ?string $slug = 'select-company';

    protected static ?int $navigationSort = -1;

    protected static string $view = 'filament.pages.company-selector';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'selectedCompany' => session('selected_company_id', 'all'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedCompany')
                    ->label('Company')
                    ->options(function (): array {
                        $companies = Company::query()
                            ->get(['id', 'name'])
                            ->mapWithKeys(function ($company) {
                                return [(string) $company->id => $company->name];
                            })
                            ->all();

                        return ['all' => 'All Companies'] + $companies;
                    })
                    ->reactive()
                    ->afterStateUpdated(function (?string $state) {
                        session(['selected_company_id' => $state]);
                        $this->redirect(request()->header('Referer'));
                    }),
            ])
            ->statePath('data');
    }

    public static function getNavigationItems(): array
    {
        return [
            NavigationItem::make()
                ->icon('heroicon-o-building-office')
                ->label('Select Company')
                ->badge(static::getSelectedCompanyName())
                ->url(static::getUrl())
                ->sort(-1),
        ];
    }

    protected static function getSelectedCompanyName(): string
    {
        $selectedCompanyId = session('selected_company_id', 'all');

        if ($selectedCompanyId === 'all') {
            return 'All Companies';
        }

        $company = Company::find($selectedCompanyId);
        if (! $company) {
            return 'All Companies';
        }

        return Str::limit($company->name, 15);
    }
}
