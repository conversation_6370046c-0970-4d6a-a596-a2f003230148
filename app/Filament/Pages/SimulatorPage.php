<?php

namespace App\Filament\Pages;

use App\Jobs\Simulation\SimulateRideJob;
use App\Models\Plan\Schedule;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class SimulatorPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-play';

    protected static ?string $title = 'Ride Simulator';

    protected static string $view = 'filament.pages.simulator-page';

    protected static ?string $slug = 'simulator';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('schedule_id')
                    ->label('Schedule')
                    ->options(
                        Schedule::query()
                            ->get()
                            ->mapWithKeys(fn ($schedule) => [
                                $schedule->id => "{$schedule->name} [{$schedule->id}]",
                            ])
                            ->toArray()
                    )
                    ->required(),
                Select::make('speed')
                    ->options([
                        'slow' => 'Slow',
                        'normal' => 'Normal',
                        'fast' => 'Fast',
                    ])
                    ->default('normal'),
                Toggle::make('allow_absence')
                    ->label('Allow Absence'),
                Toggle::make('allow_driver_stop_skip')
                    ->label('Allow Driver Stop Skip'),
                Toggle::make('allow_passenger_stop_cancel')
                    ->label('Allow Passenger Stop Cancel'),
                Toggle::make('allow_driver_cancel_ride')
                    ->label('Allow Driver Cancel Ride'),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('simulate')
                ->action(function () {
                    if (app()->environment('production')) {
                        return;
                    }

                    SimulateRideJob::dispatch(
                        scheduleId: $this->data['schedule_id'],
                        speed: $this->data['speed'] ?? 'normal',
                        allowAbsence: $this->data['allow_absence'] ?? true,
                        allowDriverStopSkip: $this->data['allow_driver_stop_skip'] ?? false,
                        allowPassengerStopCancel: $this->data['allow_passenger_stop_cancel'] ?? false,
                        allowDriverCancelRide: $this->data['allow_driver_cancel_ride'] ?? false,
                    );

                    Notification::make()
                        ->title('Simulation started')
                        ->success()
                        ->send();
                }),
        ];
    }
}
