<?php

namespace App\Filament\Widgets;

use App\Models\Transport\Stop;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class StopChartWidget extends ChartWidget
{
    use InteractsWithPageFilters;

    protected static ?string $heading = 'Stops';

    //    protected int | string | array $columnSpan = 12;

    protected function getData(): array
    {
        $startDate = $this->filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $this->filters['end_date'] ?? now()->endOfMonth()->toDateString();

        $stationsQuery = Stop::query()
            ->whereBetween('scheduled_at', [$startDate, $endDate])
            ->selectRaw('DATE(scheduled_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date');

        //        if (!is_null($this->filters['company_id']) and is_int($this->filters['company_id'])) {
        //            $stationsQuery = $stationsQuery->where('company_id', $this->filters['company_id']);
        //        }

        $stations = $stationsQuery->get();

        $labels = $stations->pluck('date')->toArray();
        $data = $stations->pluck('count')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Stops',
                    'data' => $data,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
