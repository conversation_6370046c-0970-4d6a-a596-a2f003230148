<?php

namespace App\Filament\Forms\Concerns;

use App\Enums\General\Country;
use App\Models\Auth\User;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Model;

trait HasLocationFields
{
    protected static function locationFields(): array
    {
        return [
            TextInput::make('name')->nullable(),
            TextInput::make('lat')
                ->numeric()
                ->required()
                ->rules(['decimal:0,6', 'between:-90,90']),
            TextInput::make('lng')
                ->numeric()
                ->required()
                ->rules(['decimal:0,6', 'between:-180,180']),
            TextInput::make('street_line_1')->nullable(),
            TextInput::make('street_line_2')->nullable(),
            TextInput::make('city')->nullable(),
            TextInput::make('state')->nullable(),
            Select::make('country')
                ->label('Country')
                ->options(Country::asSelectArray()),
            TextInput::make('postal_code')->nullable(),
            Select::make('company_id')
                ->label('Company')
                ->relationship('company', 'name')
                ->searchable()
                ->preload()
                ->required(),
            MorphToSelect::make('creator')
                ->required()
                ->types([
                    MorphToSelect\Type::make(User::class)
                        ->getOptionLabelFromRecordUsing(fn (User $record): string => "{$record->name}"),
                ]),
        ];
    }

    public static function getLocationSelect(string $field, string $label, bool $required = true, string $relation = 'location'): Select
    {
        return Select::make($field)
            ->label($label)
            ->relationship($relation, 'name')
            ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record?->name}")
            ->searchable()
            ->preload()
            ->required($required)
            ->createOptionForm(self::locationFields())
            ->editOptionForm(self::locationFields());
    }
}
