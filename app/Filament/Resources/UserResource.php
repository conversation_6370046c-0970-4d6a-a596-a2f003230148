<?php

namespace App\Filament\Resources;

use App\Enums\Auth\UserGender;
use App\Enums\Auth\UserStatus;
use App\Enums\General\Locale;
use App\Enums\Notification\NotificationChannel;
use App\Filament\Resources\UserResource\Pages;
use App\Models\Auth\User;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class UserResource extends ScopedResource
{
    protected static ?string $model = User::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static bool $shouldSkipAuthorization = true;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationBadgeTooltip = 'Total users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('first_name')
                    ->required()
                    ->label('First Name'),

                TextInput::make('last_name')
                    ->required()
                    ->label('Last Name'),

                TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->label('Email Address'),

                TextInput::make('phone')
                    ->tel()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->label('Phone Number'),

                TextInput::make('password')
                    ->password()
                    ->required()
                    ->hiddenOn('edit')
                    ->label('Password'),

                Select::make('gender')
                    ->nullable()
                    ->options(UserGender::asSelectArray())
                    ->label('Gender'),

                Select::make('locale')
                    ->default('en')
                    ->options(Locale::asSelectArray())
                    ->label('Preferred Locale'),

                Select::make('status')
                    ->default(UserStatus::Active)
                    ->options(UserStatus::asSelectArray())
                    ->label('Status'),

                Select::make('channel')
                    ->nullable()
                    ->options(NotificationChannel::asSelectArray())
                    ->label('Notification Channel'),

                Select::make('region_id')
                    ->relationship('region', 'name')
                    ->nullable()
                    ->label('Region'),

                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required()
                    ->label('Company'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('first_name')->sortable()->searchable(),
                TextColumn::make('last_name')->sortable()->searchable(),
                TextColumn::make('email')->sortable()->searchable(),
                TextColumn::make('phone')->sortable()->searchable(),
                TextColumn::make('locale')->label('Locale'),
                TextColumn::make('status')->label('Status'),
                TextColumn::make('region.name')->sortable()->label('Region'),
                TextColumn::make('company_id')->sortable()->label('Company Id'),
                TextColumn::make('created_at')->sortable()->dateTime(),
                TextColumn::make('updated_at')->sortable()->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('User Status')
                    ->options(UserStatus::asSelectArray()),

                SelectFilter::make('locale')
                    ->label('Locale')
                    ->options(Locale::asSelectArray()),

                SelectFilter::make('company_id')
                    ->label('Company')
                    ->relationship('company', 'name'),

                SelectFilter::make('region_id')
                    ->label('Region')
                    ->relationship('region', 'name'),

                TrashedFilter::make(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                Impersonate::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    // public static function getEloquentQuery(): Builder
    // {
    //     return parent::getEloquentQuery()
    //         ->withoutGlobalScopes([
    //             SoftDeletingScope::class,
    //         ]);
    // }
}
