<?php

namespace App\Filament\Resources;

use App\Enums\Auth\UserGender;
use App\Enums\Auth\UserStatus;
use App\Enums\General\Locale;
use App\Filament\Resources\ResponsibleResource\Pages;
use App\Models\Transport\Responsible;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Validation\Rule;

class ResponsibleResource extends ScopedResource
{
    protected static ?string $model = Responsible::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Transport';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('reference')
                    ->label('Reference')
                    ->nullable()
                    ->maxLength(255),
                TextInput::make('first_name')
                    ->label('First Name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('last_name')
                    ->label('Last Name')
                    ->required()
                    ->maxLength(255),
                DatePicker::make('birthdate')
                    ->label('Birthdate')
                    ->nullable(),
                Select::make('gender')
                    ->label('Gender')
                    ->options(UserGender::asSelectArray())
                    ->nullable(),
                TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->nullable()
                    ->maxLength(255),
                TextInput::make('phone')
                    ->label('Phone')
                    ->tel()
                    ->required()
                    ->rules(function ($get) {
                        return ['string', Rule::unique('responsibles', 'phone')->ignore($get('id'))];
                    })
                    ->maxLength(15),
                TextInput::make('channel')
                    ->label('Channel')
                    ->nullable(),
                Select::make('locale')
                    ->label('Locale')
                    ->required()
                    ->options(Locale::asSelectArray())
                    ->default('en'),
                Select::make('status')
                    ->label('Status')
                    ->options(UserStatus::asSelectArray())
                    ->nullable(),
                Select::make('region_id')
                    ->label('Region')
                    ->relationship('region', 'name')
                    ->nullable(),
                Select::make('company_id')
                    ->label('Company')
                    ->relationship('company', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('reference')
                    ->label('Reference'),
                TextColumn::make('first_name')
                    ->label('First Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_name')
                    ->label('Last Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('birthdate')
                    ->label('Birthdate')
                    ->sortable(),
                TextColumn::make('gender')
                    ->label('Gender')
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->sortable(),
                TextColumn::make('phone')
                    ->label('Phone')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->sortable(),
                TextColumn::make('created_at')->sortable()->dateTime(),
                TextColumn::make('updated_at')->sortable()->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(UserStatus::asSelectArray())
                    ->label('Status'),
                SelectFilter::make('gender')
                    ->options(UserGender::asSelectArray())
                    ->label('Gender'),
                SelectFilter::make('region_id')
                    ->relationship('region', 'name')
                    ->label('Region'),
                SelectFilter::make('company_id')
                    ->relationship('company', 'name')
                    ->label('Company'),
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListResponsibles::route('/'),
            'create' => Pages\CreateResponsible::route('/create'),
            'edit' => Pages\EditResponsible::route('/{record}/edit'),
        ];
    }
}
