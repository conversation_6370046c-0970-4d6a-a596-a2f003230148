<?php

namespace App\Filament\Resources;

use App\Enums\Auth\UserGender;
use App\Enums\General\Locale;
use App\Enums\Notification\NotificationChannel;
use App\Enums\Transport\PassengerStatus;
use App\Filament\Resources\PassengerResource\Pages\CreatePassenger;
use App\Filament\Resources\PassengerResource\Pages\EditPassenger;
use App\Filament\Resources\PassengerResource\Pages\ListPassengers;
use App\Filament\Resources\PassengerResource\RelationManagers\LocationsRelationManager;
use App\Filament\Resources\PassengerResource\RelationManagers\ResponsiblesRelationManager;
use App\Models\Transport\Passenger;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PassengerResource extends ScopedResource
{
    protected static ?string $model = Passenger::class;

    protected static bool $shouldSkipAuthorization = true;

    protected static ?string $navigationGroup = 'Transport';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('reference')
                    ->nullable(),
                TextInput::make('first_name')
                    ->required(),
                TextInput::make('last_name')
                    ->required(),
                DatePicker::make('birthdate')
                    ->nullable(),
                Select::make('gender')
                    ->options(UserGender::asSelectArray())
                    ->nullable(),
                TextInput::make('email')
                    ->email()
                    ->nullable()
                    ->unique(ignoreRecord: true),
                TextInput::make('phone')
                    ->tel()
                    ->nullable()
                    ->unique(ignoreRecord: true),
                Select::make('channel')
                    ->options(NotificationChannel::asSelectArray())
                    ->nullable(),
                Select::make('locale')
                    ->options(Locale::asSelectArray())
                    ->default('en'),
                Select::make('status')
                    ->options(PassengerStatus::asSelectArray())
                    ->default('active'),
                Select::make('region_id')
                    ->relationship('region', 'name')
                    ->nullable(),
                Select::make('group_id')
                    ->relationship('group', 'name')
                    ->nullable(),
                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('reference')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('first_name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('last_name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('email')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('phone')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->colors([
                        'success' => PassengerStatus::Active,
                        'warning' => PassengerStatus::Inactive,
                    ]),
                TextColumn::make('created_at')
                    ->dateTime('d-M-Y H:i')
                    ->sortable(),
                TextColumn::make('updated_at')
                    ->dateTime('d-M-Y H:i')
                    ->sortable(),
                TextColumn::make('deleted_at')
                    ->dateTime('d-M-Y H:i')
                    ->sortable(),

            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(PassengerStatus::asSelectArray()),
                SelectFilter::make('gender')
                    ->options(UserGender::asSelectArray()),
                SelectFilter::make('region_id')
                    ->relationship('region', 'name')
                    ->label('Region'),
                SelectFilter::make('group_id')
                    ->relationship('group', 'name')
                    ->label('Group'),
                TrashedFilter::make(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                DeleteBulkAction::make(),
                ForceDeleteBulkAction::make(),
                RestoreBulkAction::make(),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            LocationsRelationManager::class,
            ResponsiblesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPassengers::route('/'),
            'create' => CreatePassenger::route('/create'),
            'edit' => EditPassenger::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        $query->withoutGlobalScopes([
            SoftDeletingScope::class,
        ]);

        return $query;
    }
}
