<?php

namespace App\Filament\Resources;

use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use App\Filament\Resources\RideResource\Pages;
use App\Models\Transport\Ride;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class RideResource extends ScopedResource
{
    protected static ?string $model = Ride::class;

    protected static ?string $navigationGroup = 'Transport';

    protected static bool $shouldSkipAuthorization = true;

    protected static ?string $navigationIcon = 'heroicon-o-sun';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('status')
                    ->options(RideStatus::asSelectArray())
                    ->required(),
                Textarea::make('note')->maxLength(65535),
                Select::make('mode')->required()->options(RideMode::asSelectArray()),
                DateTimePicker::make('started_at')
                    ->required(),
                DateTimePicker::make('arrived_at'),
                DateTimePicker::make('scheduled_at'),
                TextInput::make('start_lat')
                    ->required()
                    ->numeric()
                    ->maxValue(90)
                    ->minValue(-90),
                TextInput::make('start_lng')
                    ->required()
                    ->numeric()
                    ->maxValue(180)
                    ->minValue(-180),
                TextInput::make('end_lat')
                    ->numeric()
                    ->maxValue(90)
                    ->minValue(-90),
                TextInput::make('end_lng')
                    ->numeric()
                    ->maxValue(180)
                    ->minValue(-180),
                TextInput::make('distance')
                    ->numeric(),
                Select::make('route_id')
                    ->relationship('route', 'name')
                    ->required(),
                Select::make('driver_id')
                    ->relationship('driver', 'last_name')
                    ->required(),
                Select::make('vehicle_id')
                    ->relationship('vehicle', 'name')
                    ->required(),
                Select::make('schedule_id')
                    ->required()
                    ->relationship('schedule', 'id'),
                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('driver.last_name')->sortable()->searchable(),
                TextColumn::make('status')->badge()->color(fn (string $state): string => match ($state) {
                    RideStatus::Ongoing => 'info',
                    RideStatus::Canceled => 'danger',
                    RideStatus::Arrived => 'success',
                })->sortable()->searchable(),
                TextColumn::make('mode')->badge()->color(fn (string $state): string => match ($state) {
                    RideMode::DropOff => 'info',
                    RideMode::PickUp => 'success',
                })->searchable(),
                TextColumn::make('started_at')->sortable(),
                TextColumn::make('arrived_at')->sortable(),
                TextColumn::make('scheduled_at')->sortable(),
                TextColumn::make('distance')->sortable(),
                TextColumn::make('route.name')->sortable()->searchable(),
                TextColumn::make('company.name')->sortable()->searchable(),
                TextColumn::make('created_at')->sortable()->dateTime(),
                TextColumn::make('updated_at')->sortable()->dateTime(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRides::route('/'),
            'create' => Pages\CreateRide::route('/create'),
            'edit' => Pages\EditRide::route('/{record}/edit'),
        ];
    }
}
