<?php

namespace App\Filament\Resources;

use App\Enums\General\ModelType;
use App\Enums\Plan\AttendanceStatus;
use App\Filament\Resources\AttendanceResource\Pages;
use App\Models\Auth\User;
use App\Models\Plan\Attendance;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Models\Transport\Ride;
use Filament\Forms;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AttendanceResource extends ScopedResource
{
    protected static ?string $model = Attendance::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationGroup = 'Plan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->options(AttendanceStatus::asSelectArray())
                    ->required()
                    ->native(false),

                Forms\Components\DateTimePicker::make('date')
                    ->required(),

                Forms\Components\Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live(),

                Forms\Components\Select::make('schedule_id')
                    ->relationship(
                        name: 'schedule',
                        titleAttribute: 'name',
                        modifyQueryUsing: fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                    )
                    ->required()
                    ->searchable()
                    ->preload(),

                Forms\Components\Select::make('passenger_id')
                    ->relationship(
                        name: 'passenger',
                        titleAttribute: 'reference',
                        modifyQueryUsing: fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                    )
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                    ->required()
                    ->searchable()
                    ->preload(),

                Forms\Components\Select::make('ride_id')
                    ->relationship(
                        name: 'ride',
                        titleAttribute: 'id',
                        modifyQueryUsing: fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                    )
                    ->getOptionLabelFromRecordUsing(
                        fn (Ride $ride) => "Ride #{$ride->id} - ".$ride->departure_time?->format('Y-m-d H:i')
                    )
                    ->nullable()
                    ->searchable()
                    ->preload(),

                MorphToSelect::make('creator')
                    ->types([
                        MorphToSelect\Type::make(ModelType::User)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (User $record): string => "{$record->name}"
                            ),
                        MorphToSelect\Type::make(ModelType::Passenger)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (Passenger $record): string => "{$record->name}"
                            ),
                        MorphToSelect\Type::make(ModelType::Responsible)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (Responsible $record): string => "{$record->name}"
                            ),
                    ])
                    ->searchable()
                    ->preload()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('ride.note')
                    ->label('Ride')
                    ->default(fn (Attendance $record) => $record->ride_id ? "Ride #{$record->ride_id}" : '-'),

                Tables\Columns\TextColumn::make('passenger.name')
                    ->label('Passenger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By'),

                Tables\Columns\TextColumn::make('schedule.name')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'absent' => 'danger',
                        'no_show' => 'danger',
                        'present' => 'success',
                        'expected' => 'warning',
                        'skipped' => 'info',
                        'canceled' => 'dark',
                    }),

                Tables\Columns\TextColumn::make('date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('company.name')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(AttendanceStatus::asSelectArray()),

                Tables\Filters\SelectFilter::make('passenger_id')
                    ->relationship('passenger', 'reference')
                    ->searchable()
                    ->preload()
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->first_name} {$record->last_name} ({$record->reference})"),

                Tables\Filters\SelectFilter::make('company_id')
                    ->relationship('company', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('schedule_id')
                    ->relationship('schedule', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('to'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query) => $query->whereDate('date', '>=', $data['from'])
                            )
                            ->when(
                                $data['to'],
                                fn (Builder $query) => $query->whereDate('date', '<=', $data['to'])
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttendances::route('/'),
            'create' => Pages\CreateAttendance::route('/create'),
            'edit' => Pages\EditAttendance::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
