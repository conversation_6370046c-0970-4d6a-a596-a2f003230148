<?php

namespace App\Filament\Resources;

use App\Enums\Fleet\VehicleStatus;
use App\Enums\Fleet\VehicleType;
use App\Filament\Resources\VehicleResource\Pages\CreateVehicle;
use App\Filament\Resources\VehicleResource\Pages\EditVehicle;
use App\Filament\Resources\VehicleResource\Pages\ListVehicles;
use App\Models\Auth\User;
use App\Models\Fleet\Vehicle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class VehicleResource extends ScopedResource
{
    protected static ?string $model = Vehicle::class;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?string $navigationGroup = 'Fleet';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('reference')
                    ->nullable()
                    ->rules(['string']),

                TextInput::make('name')
                    ->required()
                    ->rules(['string']),

                TextInput::make('make')
                    ->required()
                    ->rules(['string']),

                Select::make('type')
                    ->required()
                    ->options(VehicleType::asSelectArray())
                    ->rules([Rule::in(VehicleType::getValues())]),

                Select::make('status')
                    ->required()
                    ->options(VehicleStatus::asSelectArray())
                    ->rules([Rule::in(VehicleStatus::getValues())]),

                TextInput::make('plate')
                    ->nullable()
                    ->rules(['string']),

                TextInput::make('capacity')
                    ->required()
                    ->integer()
                    ->rules(['integer']),

                Select::make('region_id')
                    ->relationship('region', 'name')
                    ->nullable()
                    ->rules(['integer', 'exists:regions,id']),

                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required()
                    ->rules(['integer', 'exists:companies,id'])
                    ->reactive()
                    ->afterStateUpdated(function (callable $set) {
                        $set('created_by', null);
                    }),

                Select::make('created_by')
                    ->label('Created By')
                    ->options(function (callable $get) {
                        $companyId = $get('company_id');

                        if ($companyId) {
                            return User::where('company_id', $companyId)
                                ->get()
                                ->mapWithKeys(function ($user) {
                                    return [$user->id => "{$user->first_name} {$user->last_name}"];
                                });
                        }

                        return [];
                    })
                    ->searchable()
                    ->required(),
            ]);
    }

    public static function getTitle($record): string
    {
        // Customize how the title is displayed
        return "{$record->first_name} {$record->last_name}";
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable()->searchable(),
                TextColumn::make('reference')->sortable()->searchable(),
                TextColumn::make('name')->sortable()->searchable(),
                TextColumn::make('make')->sortable()->searchable(),
                TextColumn::make('plate')->sortable()->searchable(),
                TextColumn::make('status')->sortable()->searchable(),
                TextColumn::make('capacity')->sortable()->searchable(),
                TextColumn::make('region.name')->sortable()->searchable(),
                TextColumn::make('company.name')->sortable()->searchable(),
                TextColumn::make('created_at')->sortable()->dateTime(),
                TextColumn::make('updated_at')->sortable()->dateTime(),
            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(VehicleStatus::asSelectArray())
                    ->label('Status'),
                SelectFilter::make('region_id')
                    ->relationship('region', 'name')
                    ->label('Region'),
                SelectFilter::make('company_id')
                    ->relationship('company', 'name')
                    ->label('Company'),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListVehicles::route('/'),
            'create' => CreateVehicle::route('/create'),
            'edit' => EditVehicle::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
