<?php

namespace App\Filament\Resources;

use App\Enums\Fleet\DriverStatus;
use App\Enums\General\Locale;
use App\Enums\Notification\NotificationChannel;
use App\Filament\Resources\DriverResource\Pages;
use App\Models\Fleet\Driver;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class DriverResource extends ScopedResource
{
    protected static ?string $model = Driver::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationGroup = 'Fleet';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('reference')
                    ->nullable()
                    ->rules(['string']),
                TextInput::make('first_name')
                    ->required()
                    ->rules(['string']),
                TextInput::make('last_name')
                    ->required()
                    ->rules(['string']),
                TextInput::make('phone')
                    ->required()
                    ->tel()
                    ->rules(function ($get) {
                        return ['string', Rule::unique('drivers', 'phone')
                            ->ignore($get('id'))];
                    }),
                Select::make('status')
                    ->required()
                    ->options(DriverStatus::asSelectArray())
                    ->rules([Rule::in(DriverStatus::getValues())]),
                Select::make('locale')
                    ->required()
                    ->options(Locale::asSelectArray()),
                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required(),
                TextInput::make('email')
                    ->nullable()
                    ->rules(function ($get) {
                        return ['string', Rule::unique('drivers', 'email')
                            ->ignore($get('id'))];
                    }),
                Select::make('region_id')
                    ->relationship('region', 'name')
                    ->nullable()
                    ->rules(['integer', 'exists:regions,id']),
                Select::make('default_vehicle_id')
                    ->relationship('defaultVehicle', 'name')
                    ->nullable()
                    ->rules(['integer', 'exists:vehicles,id']),
                Select::make('channel')
                    ->options(NotificationChannel::asSelectArray()),
            ]);
    }

    public static function getTitle($record): string
    {
        // Customize how the title is displayed
        return "{$record->first_name} {$record->last_name}";
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('reference')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('first_name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('last_name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('email')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('phone')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('status')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('region.name')
                    ->sortable()
                    ->searchable()
                    ->label('Region'),
                TextColumn::make('defaultVehicle.name')
                    ->sortable()
                    ->searchable()
                    ->label('Default Vehicle'),
                TextColumn::make('locale')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('company.name')
                    ->sortable()
                    ->searchable()
                    ->label('Company'),
                TextColumn::make('created_at')
                    ->sortable()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->sortable()
                    ->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(DriverStatus::asSelectArray())
                    ->label('Status'),
                SelectFilter::make('region_id')
                    ->relationship('region', 'name')
                    ->label('Region'),
                SelectFilter::make('company_id')
                    ->relationship('company', 'name')
                    ->label('Company'),
                SelectFilter::make('default_vehicle_id')
                    ->relationship('defaultVehicle', 'name')
                    ->label('Default Vehicle'),

                TrashedFilter::make(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                DeleteBulkAction::make(),
                ForceDeleteBulkAction::make(),
                RestoreBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrivers::route('/'),
            'create' => Pages\CreateDriver::route('/create'),
            'edit' => Pages\EditDriver::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
