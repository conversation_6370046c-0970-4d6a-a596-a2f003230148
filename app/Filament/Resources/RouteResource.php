<?php

namespace App\Filament\Resources;

use App\Enums\Transport\RouteStatus;
use App\Filament\Forms\Concerns\HasLocationFields;
use App\Filament\Resources\RouteResource\Pages;
use App\Models\Transport\Route;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RouteResource extends ScopedResource
{
    use HasLocationFields;

    protected static ?string $model = Route::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path-rounded-square';

    protected static ?string $navigationGroup = 'Transport';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('reference')
                    ->label('Reference')
                    ->nullable()
                    ->maxLength(255),

                TextInput::make('name')
                    ->label('Route Name')
                    ->required()
                    ->maxLength(255),

                static::getLocationSelect(
                    field: 'origin_id',
                    label: 'Origin Location',
                    relation: 'origin'
                ),

                static::getLocationSelect(
                    field: 'destination_id',
                    label: 'Destination Location',
                    relation: 'destination'
                ),

                Select::make('status')
                    ->label('Status')
                    ->options(RouteStatus::asSelectArray())
                    ->required(),

                Select::make('region_id')
                    ->label('Region')
                    ->relationship('region', 'name')
                    ->nullable(),

                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),

                Select::make('created_by')
                    ->label('Created By')
                    ->relationship('createdBy', 'name')
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                    ->searchable()
                    ->preload()
                    ->required(),

                Toggle::make('static')
                    ->label('Is Static?')
                    ->default(false),

                Section::make('Stations')
                    ->schema([
                        Repeater::make('stations')
                            ->relationship('stations')
                            ->schema([
                                TextInput::make('name')
                                    ->nullable()
                                    ->maxLength(255),

                                TextInput::make('order')
                                    ->integer()
                                    ->required()
                                    ->default(fn (Get $get) => $get('../../stations') ? count($get('../../stations')) : 1),

                                static::getLocationSelect(
                                    field: 'location_id',
                                    label: 'Location',
                                ),

                                Select::make('passengers')
                                    ->multiple()
                                    ->relationship('passengers', 'name')
                                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                                    ->pivotData(fn (Get $get) => [
                                        'route_id' => $get('../../id'),
                                        'company_id' => $get('../../company_id'),
                                        'created_at' => now(),
                                    ])
                                    ->preload()
                                    ->searchable()
                                    ->required(),

                                TextInput::make('duration')
                                    ->integer()
                                    ->nullable(),

                                TextInput::make('distance')
                                    ->integer()
                                    ->nullable(),

                                Select::make('company_id')
                                    ->relationship('company', 'name')
                                    ->preload()
                                    ->searchable()
                                    ->required(),

                                Select::make('created_by')
                                    ->relationship('createdBy', 'name')
                                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                                    ->preload()
                                    ->searchable()
                                    ->required(),

                            ])

                            ->orderColumn('order')
                            ->defaultItems(0)
                            ->reorderable()
                            ->collapsible()
                            ->cloneable()
                            ->columns(2)
                            ->grid(1)
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? null)
                            // ->reorderable(fn(Get $get) => $get('static') === true)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->hiddenOn('create')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('reference')
                    ->label('Reference'),
                TextColumn::make('name')
                    ->label('Route Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('origin.name')
                    ->label('Origin')
                    ->sortable(),
                TextColumn::make('destination.name')
                    ->label('Destination')
                    ->sortable(),
                TextColumn::make('stations_count')
                    ->label('Stations')
                    ->counts('stations')
                    ->alignCenter()
                    ->badge()
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->sortable(),
                IconColumn::make('static')
                    ->label('Is Static')
                    ->boolean(),
                TextColumn::make('created_at')
                    ->sortable()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->sortable()
                    ->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),
                SelectFilter::make('region_id')
                    ->relationship('region', 'name'),
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoutes::route('/'),
            'create' => Pages\CreateRoute::route('/create'),
            'edit' => Pages\EditRoute::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
