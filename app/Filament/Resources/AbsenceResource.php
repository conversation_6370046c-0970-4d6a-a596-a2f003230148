<?php

namespace App\Filament\Resources;

use App\Enums\General\ModelType;
use App\Filament\Resources\AbsenceResource\Pages;
use App\Models\Auth\User;
use App\Models\Plan\Absence;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use Filament\Forms;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AbsenceResource extends ScopedResource
{
    protected static ?string $model = Absence::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationGroup = 'Plan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\DateTimePicker::make('from')
                    ->required(),

                Forms\Components\DateTimePicker::make('to')
                    ->required(),

                Forms\Components\Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live(),

                Forms\Components\Select::make('passenger_id')
                    ->relationship(
                        name: 'passenger',
                        titleAttribute: 'reference',
                        modifyQueryUsing: fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                    )
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                    ->required()
                    ->searchable()
                    ->preload(),

                MorphToSelect::make('creator')
                    ->types([
                        MorphToSelect\Type::make(ModelType::User)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (User $record): string => "{$record->name}"
                            ),
                        MorphToSelect\Type::make(ModelType::Passenger)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (Passenger $record): string => "{$record->name}"
                            ),
                        MorphToSelect\Type::make(ModelType::Responsible)
                            ->titleAttribute('name')
                            ->modifyOptionsQueryUsing(
                                fn (Builder $query, Forms\Get $get) => $query->where('company_id', $get('company_id'))
                            )
                            ->getOptionLabelFromRecordUsing(
                                fn (Responsible $record): string => "{$record->name}"
                            ),
                    ])
                    ->searchable()
                    ->preload()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('passenger.name')
                    ->label('Passenger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By'),

                Tables\Columns\TextColumn::make('from')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('to')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('company.name')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('passenger_id')
                    ->relationship('passenger', 'reference')
                    ->searchable()
                    ->preload()
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}"),

                Tables\Filters\SelectFilter::make('company_id')
                    ->relationship('company', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('to'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query) => $query->whereDate('from', '>=', $data['from'])
                            )
                            ->when(
                                $data['to'],
                                fn (Builder $query) => $query->whereDate('to', '<=', $data['to'])
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAbsences::route('/'),
            'create' => Pages\CreateAbsence::route('/create'),
            'edit' => Pages\EditAbsence::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
