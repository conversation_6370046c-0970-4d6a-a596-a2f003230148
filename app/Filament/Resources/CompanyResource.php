<?php

namespace App\Filament\Resources;

use App\Enums\Company\Industry;
use App\Enums\General\Timezone;
use App\Enums\System\Plugin;
use App\Filament\Forms\Concerns\HasLocationFields;
use App\Filament\Resources\CompanyResource\Pages;
use App\Models\Company\Company;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompanyResource extends Resource
{
    use HasLocationFields;

    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    public static function form(Form $form): Form
    {
        $preferenceFields = collect(config('preferences', []))
            ->map(function ($config, $key) {
                return match ($config['type']) {
                    'select' => Select::make("preferences.{$key}")
                        ->label(str($key)->replace('_', ' ')->title())
                        ->options($config['options']),
                    'number' => TextInput::make("preferences.{$key}")
                        ->label(str($key)->replace('_', ' ')->title())
                        ->numeric(),
                    'text' => TextInput::make("preferences.{$key}")
                        ->label(str($key)->replace('_', ' ')->title()),
                    default => null
                };
            })
            ->filter()
            ->toArray();

        return $form
            ->schema([
                TextInput::make('reference')
                    ->nullable(),
                TextInput::make('name')
                    ->required(),
                Select::make('industry')
                    ->required()
                    ->options(Industry::asSelectArray()),
                TextInput::make('locale')
                    ->default('en')
                    ->required(),
                Select::make('timezone')
                    ->options(collect(Timezone::asOptions())->mapWithKeys(function ($option) {
                        return [$option['id'] => $option['name']];
                    })->toArray())
                    ->searchable()
                    ->preload()
                    ->nullable(),
                Select::make('emergency_contact_id')
                    ->relationship('emergencyContact', 'name')
                    ->getOptionLabelFromRecordUsing(fn (Model $record) => "{$record->name}")
                    ->nullable(),

                // TODO: fix weird bug with location edit country is empty.
                static::getLocationSelect(
                    field: 'location_id',
                    label: 'Location',
                    relation: 'location',
                    required: false
                ),

                SpatieMediaLibraryFileUpload::make('logo')
                    ->collection('logo'),

                Section::make('Preferences')
                    ->schema($preferenceFields),

                Section::make('Plugins')
                    ->schema([
                        Select::make('plugins')
                            ->label('Enabled Plugins')
                            ->options(Plugin::asSelectArray())
                            ->multiple()
                            ->columnSpanFull(),
                    ]),

            ]);
    }

    public static function beforeSave(Company $record, array $data): void
    {
        $record->preferences = $data['preferences'] ?? [];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('industry')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('timezone')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('locale')
                    ->label('Locale')
                    ->sortable(),
                TextColumn::make('location.country')
                    ->label('Country')
                    ->sortable(),
                ImageColumn::make('logo')
                    ->label('Logo')
                    ->sortable(),
                TextColumn::make('emergencyContact.name')
                    ->label('Emergency Contact')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->sortable()
                    ->dateTime(),
                TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->sortable()
                    ->dateTime(),
            ])
            ->filters([
                SelectFilter::make('industry')
                    ->options(Industry::asSelectArray())
                    ->label('Industry')
                    ->searchable()
                    ->preload(),
                TrashedFilter::make(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                DeleteBulkAction::make(),
                ForceDeleteBulkAction::make(),
                RestoreBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Define any relations if necessary
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
