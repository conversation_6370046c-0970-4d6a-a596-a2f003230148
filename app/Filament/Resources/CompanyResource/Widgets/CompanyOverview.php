<?php

namespace App\Filament\Resources\CompanyResource\Widgets;

use App\Filament\Resources\CompanyResource;
use App\Filament\Resources\StopResource;
use App\Filament\Resources\SubscriptionResource;
use App\Filament\Resources\UserResource;
use App\Models\Auth\User;
use App\Models\Company\Company;
use App\Models\Subscription\Subscription;
use App\Models\Transport\Stop;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CompanyOverview extends StatsOverviewWidget
{
    use InteractsWithPageFilters;

    protected function getStats(): array
    {
        $companyId = $this->filters['company_id'] ?? null;
        $startDate = $this->filters['start_date'] ?? now()->startOfMonth()->toDateString();
        $endDate = $this->filters['end_date'] ?? now()->endOfMonth()->toDateString();

        return [
            Stat::make('Companies', Company::count())
                ->icon(CompanyResource::getNavigationIcon()),
            Stat::make(
                'Users',
                User::query()->company($companyId)->count()
            )->icon(UserResource::getNavigationIcon()),
            Stat::make(
                'Subscriptions',
                Subscription::query()->company($companyId)->count()
            )->icon(SubscriptionResource::getNavigationIcon()),
            Stat::make(
                'Stops',
                Stop::query()->company($companyId)->whereBetween('scheduled_at', [$startDate, $endDate])->count()
            )->icon(StopResource::getNavigationIcon()),
        ];
    }
}
