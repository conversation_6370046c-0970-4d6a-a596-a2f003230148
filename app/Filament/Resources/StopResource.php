<?php

namespace App\Filament\Resources;

use App\Enums\Transport\StopStatus;
use App\Filament\Resources\StopResource\Pages;
use App\Models\Transport\Stop;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class StopResource extends ScopedResource
{
    protected static ?string $model = Stop::class;

    protected static ?string $navigationGroup = 'Transport';

    protected static ?string $navigationIcon = 'heroicon-o-stop-circle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('status')
                    ->options(StopStatus::asSelectArray())
                    ->required(),
                Select::make('ride_id')
                    ->relationship('ride', 'id')
                    ->required(),
                Select::make('station_id')
                    ->relationship('station', 'id')
                    ->required(),
                DateTimePicker::make('departed_at'),
                DateTimePicker::make('arrived_at'),
                DateTimePicker::make('scheduled_at'),
                Select::make('company_id')
                    ->relationship('company', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->sortable(),
                TextColumn::make('ride.id')->sortable()->searchable(),
                TextColumn::make('station.name')->sortable()->searchable(),
                TextColumn::make('departed_at')->sortable()->searchable(),
                TextColumn::make('arrived_at')->sortable()->searchable(),
                TextColumn::make('scheduled_at')->sortable()->searchable(),
                TextColumn::make('company.name')->sortable()->searchable(),
                TextColumn::make('created_at')->sortable()->dateTime(),
                TextColumn::make('updated_at')->sortable()->dateTime(),
            ])
            ->filters([
                //                DatePicker::make('departed_at'),
                //                DatePicker::make('scheduled_at'),
                //                DatePicker::make('arrived_at'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStops::route('/'),
            'create' => Pages\CreateStop::route('/create'),
            'edit' => Pages\EditStop::route('/{record}/edit'),
        ];
    }
}
