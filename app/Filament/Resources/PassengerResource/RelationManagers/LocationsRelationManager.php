<?php

namespace App\Filament\Resources\PassengerResource\RelationManagers;

use App\Models\Auth\User;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LocationsRelationManager extends RelationManager
{
    protected static string $relationship = 'locations';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')->nullable(),
                TextInput::make('lat')
                    ->numeric()
                    ->required()
                    ->rules(['decimal:0,6', 'between:-90,90']),
                TextInput::make('lng')
                    ->numeric()
                    ->required()
                    ->rules(['decimal:0,6', 'between:-180,180']),
                TextInput::make('street_line_1')->nullable(),
                TextInput::make('street_line_2')->nullable(),
                TextInput::make('city')->nullable(),
                TextInput::make('state')->nullable(),
                TextInput::make('postal_code')->nullable(),
                Select::make('company_id')
                    ->label('Company')
                    ->relationship('company', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                MorphToSelect::make('creator')
                    ->required()
                    ->preload()
                    ->searchable()
                    ->types([
                        MorphToSelect\Type::make(User::class)
                            ->getOptionLabelFromRecordUsing(fn (User $record): string => "{$record->name}"),
                    ]),
                Toggle::make('primary')
                    ->label('Primary Location'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('street_line_1')
                    ->label('Address 1'),
                TextColumn::make('street_line_2')
                    ->label('Address 2'),

                TextColumn::make('city'),
                TextColumn::make('state'),
                TextColumn::make('postal_code'),
                TextColumn::make('lat'),
                TextColumn::make('lng'),
                IconColumn::make('primary')
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
