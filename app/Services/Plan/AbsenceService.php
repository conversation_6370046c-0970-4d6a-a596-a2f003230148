<?php

namespace App\Services\Plan;

use App\Models\Company\Company;
use App\Models\Plan\Absence;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Models\Transport\Stop;
use App\Repositories\Plan\AbsenceRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

readonly class AbsenceService
{
    public function __construct(
        private AbsenceRepository $absenceRepository,
    ) {}

    public function list(Authenticatable $user, array $filters = []): LengthAwarePaginator
    {
        return Absence::with([
            'passenger:id,first_name,last_name,status',
            'creator:id,first_name,last_name,status,company_id',
        ])
            ->company($user->company_id)
            ->filter($filters)
            ->latest()
            ->paginate()
            ->withQueryString();
    }

    public function get(Passenger $passenger, array $filters = []): Collection
    {
        return $passenger
            ->absences()
            ->filter($filters)
            ->latest()
            ->get();
    }

    public function store(array $data): Absence
    {
        return $this->absenceRepository->create($data);
    }

    public function delete(Absence $absence): ?bool
    {
        return $this->absenceRepository->delete($absence);
    }

    public function getAbsentPassengersIdsForStop(Stop $stop): Collection
    {
        $stopPassengerIds = $stop->station->passengers()->pluck('id');

        return Absence::where('from', '<=', $stop->created_at)
            ->where('to', '>=', $stop->created_at)
            ->whereIn('passenger_id', $stopPassengerIds)
            ->pluck('passenger_id');
    }

    public function getAbsentPassengersIdsForRide(Ride $ride): Collection
    {
        $ridePassengerIds = $ride->route->passengers()->pluck('id');

        return Absence::where('from', '<=', $ride->created_at)
            ->where('to', '>=', $ride->created_at)
            ->whereIn('passenger_id', $ridePassengerIds)
            ->pluck('passenger_id');
    }

    public function isAbsent(int $passengerId, Carbon $date): bool
    {
        return Absence::where('from', '<=', $date)
            ->where('to', '>=', $date)
            ->where('passenger_id', $passengerId)
            ->exists();
    }

    public function getAbsencesForDate(Company $company, Carbon $date): Collection
    {
        return Absence::where('company_id', $company->id)
            ->whereDate('from', '<=', $date)
            ->whereDate('to', '>=', $date)
            ->with(['passenger'])
            ->latest()
            ->get();
    }

    public function getTodayAbsences(Authenticatable $user): Collection
    {
        return $this->getAbsencesForDate($user->company, now());
    }
}
