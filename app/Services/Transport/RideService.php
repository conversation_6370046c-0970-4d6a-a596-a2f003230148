<?php

namespace App\Services\Transport;

use App\Enums\General\Models;
use App\Enums\Transport\RideStatus;
use App\Enums\Transport\StopStatus;
use App\Models\Company\Company;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Plan\Absence;
use App\Models\Plan\Attendance;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Models\Transport\Ride;
use App\Models\Transport\Stop;
use App\Repositories\Transport\RideRepository;
use App\Services\Plan\AttendanceService;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Collection;

readonly class RideService
{
    public function __construct(
        private RideRepository $rideRepository,
        private AttendanceService $attendanceService,
    ) {}

    public function update(Ride $ride, array $data): Ride
    {
        return $this->rideRepository->update($ride, $data);
    }

    public function arrive(Ride $ride, array $data): Ride
    {
        $data['arrived_at'] = now();
        $data['status'] = RideStatus::Arrived;
        $data['distance'] = intval($data['distance']);
        $data['scheduled_at'] = $ride->started_at->addSeconds(intval($data['duration']));

        return $this->rideRepository->update($ride, $data);
    }

    public function delete(Ride $ride): void
    {
        $this->rideRepository->delete($ride);
    }

    public function cancel(Ride $ride, array $data): Ride
    {
        $data['status'] = RideStatus::Canceled;
        $data['arrived_at'] = now();
        $data['distance'] = intval($data['distance']);

        return $this->rideRepository->update($ride, $data);
    }

    public function start(Schedule $schedule, Driver $driver, array $data): Ride
    {
        $data = [
            ...$data,
            'driver_id' => $driver->id,
            'company_id' => $driver->company_id,
            'started_at' => now(),
            'route_id' => $schedule->route_id,
            'assistant_id' => $schedule->assistant_id,
            'status' => RideStatus::Ongoing,
            'mode' => $schedule->ride_mode,
            'vehicle_id' => $schedule->vehicle_id,
        ];

        return $this->rideRepository->create($data);
    }

    public function getExpectedPassengers(Ride $ride): Collection
    {
        $absentPassengers = $this->attendanceService->getAbsentPassengerIdsForRide($ride);

        return $ride
            ->route
            ->passengers()
            ->with('responsibles')
            ->whereNotIn('passengers.id', $absentPassengers->pluck('passenger_id'))
            ->get()
            ->unique();
    }

    public function getPresentPassengers(Ride $ride): Collection
    {
        $presentPassengers = $this->attendanceService->presentForRide($ride);

        return $ride
            ->route
            ->passengers()
            ->with('responsibles')
            ->whereNotIn('passengers.id', $presentPassengers->pluck('passenger_id'))
            ->get();
    }

    public function getRidesForCalendar(Authenticatable $user, array $filters): Collection
    {
        return Ride::where('company_id', $user->company_id)
            ->filter($filters)
            ->with(['driver', 'route', 'stops', 'vehicle'])
            ->get();
    }

    public function getItems(Authenticatable $user, array $filters = []): Collection
    {
        return $user->rides()
            ->filter($filters)
            ->with([
                'vehicle',
                'route',
                'assistant',
            ])
            ->withCount([
                'presentPassengers',
                'stops',
            ])
            ->get();
    }

    public function getOngoingForUser(Authenticatable $user): ?Ride
    {
        return match ($user->type()) {
            Models::Passenger => $this->getOngoingForPassenger($user),
            Models::Assistant => $this->getOngoingForAssistant($user),
            default => null
        };
    }

    public function getOngoingForPassenger(Passenger $passenger, ?Carbon $from = null, ?Carbon $to = null): ?Ride
    {
        $routeIds = $passenger->routes()->pluck('id');

        $rideQuery = Ride::whereIn('route_id', $routeIds)
            ->where('status', RideStatus::Ongoing)
            ->with([
                'vehicle',
                'route.destination:id,name',
                'driver',
            ])
            ->latest();

        if (isset($from, $to)) {
            $rideQuery->where('created_at', '>=', $from)
                ->where('created_at', '<=', $to);
        }

        $ride = $rideQuery->first();

        if (! $ride) {
            return null;
        }

        if ($this->attendanceService->isPassengerAbsentForRide($ride, $passenger, $ride->started_at)) {
            return null;
        }

        $ride->stop = $passenger
            ->stops()
            ->with('station.location')
            ->where('ride_id', $ride?->id)
            ->first();

        return $ride;
    }

    public function getOngoingForAssistant(Assistant $assistant): ?Ride
    {
        return $assistant->rides()
            ->where('status', RideStatus::Ongoing)
            ->latest()
            ->with([
                'route.stations',
                'route.origin',
                'route.destination',
                'driver',
                'vehicle',
                'attendances',
                'stops.station.location',
                'stops.station.passengers',
            ])
            ->first();
    }

    private function getScheduledRidesForPeriod(Company $company, Carbon $from, Carbon $to): Collection
    {
        return Ride::where('company_id', $company->id)
            ->whereHas('schedule', function ($query) use ($from, $to) {
                $query->whereBetween('start_date', [$from, $to]);
            })->get();
    }

    public function getScheduledRides(Authenticatable $user, Carbon $from, Carbon $to): Collection|array
    {
        return $this->getScheduledRidesForPeriod($user->company, $from, $to);
    }

    public function haveAccess(Authenticatable $user, int|string $rideId): bool
    {
        /** @var Ride $ride */
        $ride = $this->rideRepository->find($rideId);

        if (! $ride) {
            return false;
        }

        return match ($user->type()) {
            Models::User => $user->company_id !== $ride->company_id,
            Models::Driver => $ride->hasDriver($user),
            Models::Passenger => $ride->hasPassenger($user),
            Models::Responsible => $ride->hasResponsible($user),
            default => false
        };
    }

    public function getForMap(Driver $driver): Ride
    {
        return Ride::where('driver_id', $driver->id)
            ->with([
                'route.stations',
                'route.origin',
                'route.destination',
                'driver',
                'vehicle',
                'attendances',
                'stops.station.location',
                'stops.station.passengers',
            ])->latest()
            ->first();
    }

    public function getDashboardRidesDataForPeriod(Company $company, Carbon $fromDate, Carbon $toDate): array
    {
        $startedRides = Ride::where('company_id', $company->id)
            ->where('status', RideStatus::Ongoing)
            ->with(['driver', 'vehicle'])
            ->get();

        $scheduledRides = $this->getScheduledRidesForPeriod($company, $fromDate, $toDate)
            ->load(['driver', 'vehicle', 'schedule']);

        return [
            'ongoing_rides' => $startedRides,
            'next_rides' => $scheduledRides,
        ];
    }

    public function getDashboardRidesData(Authenticatable $user): array
    {
        $currentDate = now();
        $endDate = $currentDate->copy()->addDays(5);
        return $this->getDashboardRidesDataForPeriod($user->company, $currentDate, $endDate);
    }

    public function getForMobileMap(Driver $driver): Ride
    {
        // todo optimize
        return $driver
            ->rides()
            ->with([
                'route:id,name',
                'driver:id,first_name,last_name,phone',
                'vehicle:id,type,name,plate',
            ])->withCount([
                'stations',
                'passengers',
            ])
            ->latest()
            ->first();
    }

    public function getOngoingRideForAbsence(Absence $absence): ?Ride
    {
        return $this->getOngoingForPassenger($absence->passenger, $absence->from, $absence->to);
    }

    public function getOngoingRideForAttendance(Attendance $attendance): ?Ride
    {
        return Ride::where('schedule_id', $attendance->schedule_id)
            ->whereDate('started_at', $attendance->date)
            ->where('status', RideStatus::Ongoing)
            ->first();
    }

    public function getOngoingRideForSchedule(?Schedule $schedule, Carbon $date): ?Ride
    {
        return Ride::where('schedule_id', $schedule->id)
            ->whereDate('started_at', $date)
            ->where('status', RideStatus::Ongoing)
            ->first();
    }

    public function getFinishedStationIds(Ride $ride): array
    {
        return $ride->stops()->whereIn('status', StopStatus::getFinished())->get()
            ->map(function (Stop $stop) {
                return $stop->station_id;
            })
            ->toArray();
    }
}
