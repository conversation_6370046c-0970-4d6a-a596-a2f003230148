<?php

namespace App\Services\Incident;

use App\Models\Company\Company;
use App\Models\Auth\User;
use App\Models\Incident\Incident;
use App\Repositories\Incident\IncidentRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

readonly class IncidentService
{
    public function __construct(
        private readonly IncidentRepository $incidentRepository
    ) {}

    public function get(Authenticatable $user, array $filterData = [], bool $paginate = false, array $relations = []): Collection|LengthAwarePaginator|array
    {
        $query = Incident::query()->company($user->company_id)->with($relations);

        if (! empty($filterData)) {
            $query->filter($filterData);
        }

        if ($paginate) {
            return $query->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString();
        }

        return $query->latest('created_at')->get();
    }

    public function find(int $incidentId): ?Incident
    {
        return $this->incidentRepository->find($incidentId);
    }

    public function save(array $data): Incident
    {
        return $this->incidentRepository->create($data);
    }

    public function update(Incident $incident, array $data): void
    {
        $this->incidentRepository->update($incident, $data);
    }

    public function delete(Incident $incident): void
    {
        $this->incidentRepository->delete($incident);
    }

    public function getEmergencyContactFor(Authenticatable $user): ?User
    {
        return $user?->region?->emergencyContact ?? $user->company?->emergencyContact;
    }

    public function getIncidentsForDate(Company $company, Carbon $date, int $limit = 5): Collection
    {
        return Incident::where('company_id', $company->id)
            ->whereDate('created_at', $date)
            ->with(['reporter'])
            ->latest()
            ->take($limit)
            ->get();
    }
}
