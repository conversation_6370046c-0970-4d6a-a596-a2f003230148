<?php

namespace App\Services\Analytics\Incident;

use App\Models\Company\Company;
use App\Models\Incident\Incident;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;

class IncidentAnalyticsService
{
    private function applyDefaultFilters(?array $filters): array
    {
        return [
            'from' => $filters['from'] ?? now()->subDays(30),
            'to' => $filters['to'] ?? now(),
            'group_by' => $filters['group_by'] ?? 'monthly',
        ];
    }

    private function getPreviousPeriodDates(array $filters): array
    {
        $from = Carbon::parse($filters['from'])->startOfDay();
        $to = Carbon::parse($filters['to'])->startOfDay();
        $diff = $from->diffInDays($to);

        return [
            $from->copy()->subDays($diff)->startOfDay(),
            $to->copy()->subDays($diff)->startOfDay(),
        ];
    }

    private function calculatePercentageChange($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    public function getTotalIncidents(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        $currentCount = Incident::where('company_id', $company->id)
            ->whereBetween('created_at', [$filters['from'], $filters['to']])
            ->count();

        [$previousFrom, $previousTo] = $this->getPreviousPeriodDates($filters);
        $previousCount = Incident::where('company_id', $company->id)
            ->whereBetween('created_at', [$previousFrom, $previousTo])
            ->count();

        return [
            'value' => $currentCount,
            'percentage_change' => $this->calculatePercentageChange($currentCount, $previousCount),
        ];
    }

    public function getIncidentsCountForDate(Company $company, Carbon $date): int
    {
        return Incident::where('company_id', $company->id)
            ->whereDate('created_at', $date)
            ->count();
    }

    public function getTodayIncidentsCount(Authenticatable $user): int
    {
        return $this->getIncidentsCountForDate($user->company, now());
    }
}
