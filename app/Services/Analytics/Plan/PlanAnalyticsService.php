<?php

namespace App\Services\Analytics\Plan;

use App\Models\Company\Company;
use App\Models\Plan\Absence;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;

class PlanAnalyticsService
{
    private function applyDefaultFilters(?array $filters): array
    {
        return [
            'from' => $filters['from'] ?? now()->subDays(30),
            'to' => $filters['to'] ?? now(),
            'group_by' => $filters['group_by'] ?? 'monthly',
        ];
    }

    private function getPreviousPeriodDates(array $filters): array
    {
        $from = Carbon::parse($filters['from'])->startOfDay();
        $to = Carbon::parse($filters['to'])->startOfDay();
        $diff = $from->diffInDays($to);

        return [
            $from->copy()->subDays($diff)->startOfDay(),
            $to->copy()->subDays($diff)->startOfDay(),
        ];
    }

    private function calculatePercentageChange($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    public function getTotalAbsences(Company $company, ?array $filters): array
    {
        $filters = $this->applyDefaultFilters($filters);

        $currentCount = Absence::where('company_id', $company->id)
            ->whereBetween('from', [$filters['from'], $filters['to']])
            ->count();

        [$previousFrom, $previousTo] = $this->getPreviousPeriodDates($filters);
        $previousCount = Absence::where('company_id', $company->id)
            ->whereBetween('from', [$previousFrom, $previousTo])
            ->count();

        return [
            'value' => $currentCount,
            'percentage_change' => $this->calculatePercentageChange($currentCount, $previousCount),
        ];
    }

    public function getTodayAbsencesCount(Authenticatable $user): int
    {

        return Absence::where('company_id', $user->company_id)
            ->whereDate('from', '<=', now())
            ->whereDate('to', '>=', now())
            ->count();
    }
}
