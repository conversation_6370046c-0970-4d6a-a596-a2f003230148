<?php

namespace App\Services\Api\Geo;

use App\Models\Geo\Location;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Collection;

class HereSequencingApiService
{
    private Client $client;

    private const ROUTING_API_URL = 'https://wps.hereapi.com/v8/findsequence2';

    public function __construct()
    {
        $this->client = new Client;
    }

    public function getOptimalSequence(Location $origin, Location $destination, Collection $waypoints): Collection
    {
        $query = [
            'apiKey' => config('services.here.api_key'),
            'start' => $origin->coordinates->toString(),
            'end' => $destination->coordinates->toString(),
            'mode' => 'fastest',
        ];

        $destinations = [];

        $counter = 0;

        $waypoints->each(function (Location $waypoint) use (&$query, &$counter, &$destinations) {
            $query['destination'.$counter] = $waypoint->coordinates->toString();
            $destinations['destination'.$counter] = $waypoint->id;
            $counter++;
        });

        $response = $this->client->get(self::ROUTING_API_URL, [
            RequestOptions::QUERY => $query,
        ]);

        $data = json_decode($response->getBody(), true);

        $sequence = collect($data['results'][0]['waypoints']);
        $interConnections = collect($data['results'][0]['interconnections']);

        return $sequence->map(function ($waypoint) use ($destinations, $interConnections) {
            $interConnection = $interConnections->where('toWaypoint', $waypoint['id'])->first();

            return [
                'id' => $destinations[$waypoint['id']] ?? $waypoint['id'],
                'order' => $waypoint['sequence'],
                'duration' => $interConnection['time'] ?? null,
                'distance' => $interConnection['distance'] ?? null,
            ];
        })->filter();
    }
}
