<?php

namespace App\Services\Api\Geo;

use App\Models\Geo\Location;
use GuzzleHttp\Client;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class GoogleMapsApiService
{
    private Client $client;

    private string $apiKey;

    private const DIRECTION_API_URL = 'https://maps.googleapis.com/maps/api/directions/json';

    public function __construct()
    {
        $this->client = new Client;
        $this->apiKey = config('services.google.api_key', '');
    }

    public function getETAs(string $origin, string $destination, ?Collection $waypoints, Carbon $arrivalTime): Collection
    {
        $results = collect();

        if ($waypoints->isEmpty()) {
            return $results;
        }

        $waypointIds = $waypoints->pluck('id')->toArray();

        $waypointCoordinates = $waypoints->map(function (Location $location) {
            return $location->coordinates->toString();
        });

        // Initialize variables for chunking
        $chunks = $waypointCoordinates->chunk(23);
        $previousDestination = $origin;

        foreach ($chunks as $index => $chunk) {
            $waypointsStr = $chunk->implode('|');
            $isLastChunk = $index === $chunks->count() - 1;
            $chunkDestination = $isLastChunk ? $destination : $chunk->last();

            $response = $this->client->request('GET', self::DIRECTION_API_URL, [
                'query' => [
                    'origin' => $previousDestination,
                    'destination' => $chunkDestination,
                    'waypoints' => $waypointsStr,
                    'arrival_time' => $arrivalTime->getTimestamp(),
                    'key' => $this->apiKey,
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if ($data['status'] === 'OK') {
                $route = $data['routes'][0];
                $legs = collect($route['legs']);

                $legs->each(function ($leg, $index) use (&$results, &$previousDestination, &$previousId, $waypointIds) {
                    $currentId = $waypointIds[$index] ?? null;

                    $results->push([
                        'start_location' => [
                            'lat' => $leg['start_location']['lat'],
                            'lng' => $leg['start_location']['lng'],
                            'id' => $previousId,
                        ],
                        'end_location' => [
                            'lat' => $leg['end_location']['lat'],
                            'lng' => $leg['end_location']['lng'],
                            'id' => $currentId,
                        ],
                        'duration' => $leg['duration']['value'] / 60, // duration in minutes
                    ]);

                    $previousDestination = $leg['end_location']['lat'].','.$leg['end_location']['lng'];
                    $previousId = $currentId;
                });
            }
        }

        return $results;
    }
}
