<?php

namespace App\Services\Api\Geo;

use App\Dto\Address;
use GuzzleHttp\Client;

class MapboxGeocodingApiService
{
    private Client $client;

    private const string BASE_URL = 'https://api.mapbox.com/search/geocode/v6/reverse';

    public function __construct()
    {
        $this->client = new Client;
    }

    public function geocodeCoordinates(float $latitude, float $longitude, string $lang): ?Address
    {
        $url = self::BASE_URL.'?'.http_build_query([
            'latitude' => $latitude,
            'longitude' => $longitude,
            'access_token' => config('services.mapbox.public_access_token'),
            'language' => $lang,
        ]);

        $response = $this->client->get($url);
        $data = json_decode($response->getBody()->getContents(), true);

        if (empty($data['features'][0])) {
            return null;
        }

        $address = $data['features'][0]['properties']['context'];

        return new Address(
            country: $address['country']['name'] ?? null,
            streetLine1: $address['street']['name'] ?? $address['locality']['name'] ?? null,
            streetLine2: $address['neighborhood']['name'] ?? null,
            city: $address['place']['name'] ?? null,
            state: $address['region']['name'] ?? null,
            postalCode: $address['postcode']['name'] ?? null,
        );
    }
}
