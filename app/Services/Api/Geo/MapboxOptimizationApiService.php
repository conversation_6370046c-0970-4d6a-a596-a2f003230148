<?php

namespace App\Services\Api\Geo;

use App\Models\Geo\Location;
use App\Services\Fleet\VehicleService;
use App\Services\Geo\LocationService;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Collection;

class MapboxOptimizationApiService
{
    private Client $client;

    private const string ROUTE_OPTIMISATION_V2_SUBMIT_PROBLEM_URL = 'https://api.mapbox.com/optimized-trips/v2';

    private const string ROUTE_OPTIMISATION_V2_RETRIEVE_SOLUTION_URL = 'https://api.mapbox.com/optimized-trips/v2/';

    private const string ROUTING_PROFILE = 'mapbox/driving';

    public function __construct(
        private readonly VehicleService $vehicleService,
        private readonly LocationService $locationService,
    ) {
        $this->client = new Client;
    }

    private function mapVehicles(Collection $vehicles, Location $origin, Location $destination): Collection
    {
        return $vehicles->map(function ($vehicle) {
            return [
                'name' => 'vehicle-'.$vehicle->id,
                'routing_profile' => self::ROUTING_PROFILE,
                'capacities' => [
                    'human' => $vehicle->capacity,
                ],
            ];
        })->unique('name');
    }

    private function mapPassengerLocations(Collection $passengerLocations, Location $origin, Location $destination): Collection
    {
        $mappedPassengerLocations = $passengerLocations->map(function (Location $location) {
            return [
                'name' => 'location-'.$location->id,
                'coordinates' => [
                    $location->lng,
                    $location->lat,
                ],
            ];
        });

        $mappedPassengerLocations->push([
            'name' => 'location-'.$origin->id,
            'coordinates' => [
                $origin->lng,
                $origin->lat,
            ],
        ]);

        $mappedPassengerLocations->push([
            'name' => 'location-'.$destination->id,
            'coordinates' => [
                $destination->lng,
                $destination->lat,
            ],
        ]);

        return $mappedPassengerLocations->unique('name');
    }

    private function mapPassengerToShipments(Collection $passengerLocations, Location $destination): Collection
    {
        return $passengerLocations->map(function (Location $location) use ($destination) {
            return [
                'name' => 'pickup-'.$location->id,
                'from' => 'location-'.$location->id,
                'to' => 'location-'.$destination->id,
                'size' => [
                    'human' => 1,
                ],
                'pickup_duration' => 30,
                'dropoff_duration' => 30,
            ];
        })->unique('name');
    }

    public function optimizeRoutes(Location $origin, Location $destination, Collection $passengerLocations, Collection $vehicles, Collection $drivers): Collection
    {
        $solutionId = $this->submitProblem([
            'vehicles' => $this->mapVehicles($vehicles, $origin, $destination)->toArray(),
            'locations' => $this->mapPassengerLocations($passengerLocations, $origin, $destination)->toArray(),
            'shipments' => $this->mapPassengerToShipments($passengerLocations, $destination)->toArray(),
            'version' => 1,
            'options' => [
                'objectives' => ['min-schedule-completion-time'],
            ],
        ]);

        $solution = $this->retrieveSolution($solutionId);

        return $this->formatSolution($solution, $drivers);
    }

    private function submitProblem(array $problemData): ?string
    {
        $response = $this->client->post(self::ROUTE_OPTIMISATION_V2_SUBMIT_PROBLEM_URL, [
            RequestOptions::BODY => json_encode($problemData),
            RequestOptions::QUERY => [
                'access_token' => config('services.mapbox.access_token'),
            ],
            RequestOptions::HEADERS => [
                'Content-Type' => 'application/json',
            ],
        ]);

        return json_decode($response->getBody()->getContents(), true)['id'] ?? null;
    }

    private function retrieveSolution(string $solutionId): array
    {
        $response = $this->client->get(self::ROUTE_OPTIMISATION_V2_RETRIEVE_SOLUTION_URL.$solutionId, [
            RequestOptions::QUERY => [
                'access_token' => config('services.mapbox.access_token'),
            ],
            RequestOptions::HEADERS => [
                'Content-Type' => 'application/json',
            ],
        ]);

        $response = json_decode($response->getBody()->getContents(), true);

        if (isset($response['status']) and $response['status'] == 'processing') {
            sleep(1);

            return $this->retrieveSolution($solutionId);
        }

        return $response;
    }

    public function formatSolution(array $solution, Collection $drivers): Collection
    {
        return collect($solution['routes'])->map(function ($route) use (&$drivers) {
            $stations = collect($route['stops'])->where('type', 'pickup');
            $locationIds = $stations->map(function ($station) {
                return $this->getId($station['location']);
            })->toArray();

            $vehicleId = $this->getId($route['vehicle']);
            $vehicle = $this->vehicleService->find($vehicleId);

            if ($driver = $drivers->where('default_vehicle_id', $vehicleId)->first()) {
                $drivers = $drivers->filter(function ($item) use ($driver) {
                    return $item->id !== $driver;
                });
            } else {
                $drivers = $drivers->shuffle();
                $driver = $drivers->pop();
            }

            return [
                'origin_id' => $this->getId($stations->first()['location']),
                'destination_id' => $this->getId($stations->last()['location']),
                'name' => 'Route: '.$driver->name,
                'vehicle' => $vehicle,
                'driver' => $driver,
                'passengers' => $this->locationService->getPassengersByLocationIds($locationIds),
            ];
        });
    }

    public function getId(string $key): string
    {
        return explode('-', $key)[1] ?? '';
    }
}
