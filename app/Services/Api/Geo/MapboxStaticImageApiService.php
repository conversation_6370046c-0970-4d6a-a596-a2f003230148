<?php

namespace App\Services\Api\Geo;

// todo move to service
class MapboxStaticImageApiService
{
    private const string TILESET_API_URL = 'https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/';

    private const int Zoom = 17;

    private const int Bearing = 0;

    private const int Pitch = 0;

    private const int Width = 400;

    private const int Height = 400;

    private const string MarkerColor = '307EF3';

    public function getStaticImageUrl(float $latitude, float $longitude, ?int $width, ?int $height): string
    {
        $apiKey = config('services.mapbox.public_access_token');
        $zoom = self::Zoom;
        $bearing = self::Bearing;
        $pitch = self::Pitch;
        $width = $width ?? self::Width;
        $height = $height ?? self::Height;
        $markerColor = self::MarkerColor;
        $marker = "pin-s+$markerColor($longitude,$latitude)";

        return self::TILESET_API_URL."$marker/$longitude,$latitude,$zoom,$bearing,$pitch/"."{$width}x{$height}?access_token=$apiKey";
    }
}
