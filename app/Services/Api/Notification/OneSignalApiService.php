<?php

namespace App\Services\Api\Notification;

use App\Exceptions\OneSignalNotificationException;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class OneSignalApiService
{
    private Client $client;

    private const string BASE_URL = 'https://api.onesignal.com/notifications?c=push';

    public function __construct()
    {
        $this->client = new Client;
    }

    public function pushMobileNotification(array $externalIds, array $headings, array $contents, ?string $image = null, ?string $requestId = null): void
    {
        $jsonBody = [
            'app_id' => config('services.onesignal.app_id'),
            'include_aliases' => [
                'external_id' => $externalIds,
            ],
            'headings' => $headings,
            'contents' => $contents,
            'target_channel' => 'push',
            'idempotency_key' => $requestId,
        ];

        if ($image) {
            $jsonBody['big_picture'] = $image;
            $jsonBody['ios_attachments'] = [
                'id' => $image,
            ];
        }

        Log::info('OneSignal push notification pushed'.now()->format('H:i'), $jsonBody);

        if (app()->environment('local', 'testing')) {
            return;
        }

        $response = $this->client->post(self::BASE_URL, [
            'headers' => [
                'Authorization' => 'Key '.config('services.onesignal.api_key'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $jsonBody,
        ]);

        if ($response->getStatusCode() !== 200) {
            throw new OneSignalNotificationException('Can not send push notification to OneSignal api');
        }
    }
}
