<?php

namespace App\Services\Api\Notification;

use GuzzleHttp\Client;

class SlackApiService
{
    public function __construct(private Client $client) {}

    public function send(string $text): void
    {
        $this->client->post(config('services.slack.webhook_url'), [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'blocks' => [
                    [
                        'type' => 'section',
                        'text' => [
                            'type' => 'mrkdwn',
                            'text' => $text,
                        ],
                    ],
                    [
                        'type' => 'divider',
                    ],
                ],
            ],
        ]);
    }
}
