<?php

namespace App\Services\Api\Metrics;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Log;
use Mixpanel;

class MixpanelService
{
    protected Mixpanel $mixpanel;

    public function __construct()
    {
        $this->mixpanel = Mixpanel::getInstance(config('services.mixpanel.token'));
    }

    private function getUserKey(Authenticatable $user): string
    {
        return strtoupper($user->type().'_'.$user->id);
    }

    public function trackEvent(Authenticatable $user, string $event, array $context): void
    {
        if (! app()->isProduction() or $user->isInternalUser()) {
            return;
        }

        dispatch(function () use ($user, $event, $context) {
            Log::info('Mixpanel Event: '.$event);
            $this->mixpanel->people->set(
                $this->getUserKey($user),
                [
                    'name' => $user->name,
                    'phone' => $user->phone,
                    'company_id' => $user->company_id,
                ]
            );

            $context['distinct_id'] = $this->getUserKey($user);
            $this->mixpanel->track($event, $context);
        })->afterResponse();
    }

    public function track(string $event, array $context): void
    {
        $this->mixpanel->track($event, $context);
    }

    public function peopleSet(string $distinctId, array $properties): void
    {
        $this->mixpanel->people->set($distinctId, $properties);
    }
}
