<?php

namespace App\Services\Auth;

use Illuminate\Support\Collection;
use Laravel\Sanctum\PersonalAccessToken;

class AuthService
{
    public function getAuthenticatedUsers(string $role, Collection $roleIds): Collection
    {
        return PersonalAccessToken::where('tokenable_type', $role)
            ->whereIn('tokenable_id', $roleIds)
            ->distinct()
            ->pluck('tokenable_id');
    }
}
