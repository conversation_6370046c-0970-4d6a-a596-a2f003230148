<?php

namespace App\Services\General;

use App\Enums\General\Models;
use App\Enums\Notification\NotificationType;
use App\Models\Transport\Stop;
use App\Services\Api\Geo\MapboxStaticImageApiService;

readonly class ImagePreviewService
{
    public function __construct(
        private MapboxStaticImageApiService $mapboxStaticImageApiService
    ) {}

    public function getImageUrl(?float $lat, ?float $lng, ?int $width = null, ?int $height = null, ?string $pin = null): string
    {
        if (! $lat or ! $lng) {
            return '';
        }

        return $this->mapboxStaticImageApiService->getStaticImageUrl(
            latitude: $lat,
            longitude: $lng,
            width: $width,
            height: $height,
        );
    }

    public function showImageForNotification(string $notificationType): bool
    {
        return in_array($notificationType, [
            NotificationType::StopArrived,
        ]);
    }

    public function getImageForNotification(string $notificationType, array $attributes): ?string
    {
        if (! $this->showImageForNotification($notificationType)) {
            return null;
        }

        $model = $this->getModelByNotificationType($notificationType);
        $modelId = $attributes[strtolower($model.'_id')] ?? null;

        if (! isset($modelId)) {
            return null;
        }

        $location = match ($model) {
            Models::Stop => Stop::find($modelId)?->station?->location
        };

        return $this->getImageUrl(
            lat: $location->lat,
            lng: $location->lng,
            width: 250,
            height: 150,
            pin: null
        );
    }

    public function getModelByNotificationType(string $notificationType): string
    {
        return match ($notificationType) {
            NotificationType::StopArrived => Models::Stop
        };
    }
}
