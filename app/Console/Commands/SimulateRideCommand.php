<?php

namespace App\Console\Commands;

use App\Events\Location\UpdateDriverLocationEvent;
use App\Models\Fleet\Driver;
use App\Models\Transport\Ride;
use Illuminate\Console\Command;

class SimulateRideCommand extends Command
{
    protected $signature = 'ride:simulate';

    protected $description = 'Simulate ride';

    public function handle(): void
    {

        /** @var Driver $driver */
        $driver = Driver::inRandomOrder()
            ->where('company_id', 1)
            ->where('status', 'active')
            ->first();

        $ride = Ride::find(1);

        if (is_null($driver) or is_null($ride)) {
            return;
        }

        $coordinates = $driver->coordinates;

        if (! $coordinates) {
            $driver->setCoordinates($ride->start_lat, $ride->start_lng);
            $coordinates = $driver->coordinates;
        }

        $bearing = fake()->randomElement([
            0, 90, 180, 270,
        ]);

        [$newLat, $newLng] = $this->movePoint($coordinates->lat, $coordinates->lng, 30, $bearing);

        $attributes = [
            'driver_id' => 1,
            'company_id' => 1,
            'lng' => $newLng,
            'lat' => $newLat,
            'ride_id' => 1,
            'etas' => [
                [
                    'stop_id' => 1,
                    'distance' => 2500,
                    'duration' => 100,
                ],
            ],
        ];

        broadcast(new UpdateDriverLocationEvent($attributes));

        $driver->setCoordinates($newLat, $newLng);
    }

    public static function movePoint(float $lat, float $lng, float $distance, float $bearing): array
    {
        // Radius of the Earth in meters
        $R = 6378137.0;

        // Convert latitude and longitude from degrees to radians
        $latRad = deg2rad($lat);
        $lngRad = deg2rad($lng);
        $bearingRad = deg2rad($bearing);

        // Calculate the new latitude
        $newLatRad = asin(sin($latRad) * cos($distance / $R) +
            cos($latRad) * sin($distance / $R) * cos($bearingRad));

        // Calculate the new longitude
        $newLngRad = $lngRad + atan2(
            sin($bearingRad) * sin($distance / $R) * cos($latRad),
            cos($distance / $R) - sin($latRad) * sin($newLatRad)
        );

        // Convert radians back to degrees
        $newLat = rad2deg($newLatRad);
        $newLng = rad2deg($newLngRad);

        return [$newLat, $newLng];
    }
}
