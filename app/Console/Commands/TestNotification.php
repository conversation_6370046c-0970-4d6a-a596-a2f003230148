<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use Illuminate\Console\Command;

class TestNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Notification';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //        (new Client())->post(config('services.slack.webhook_url'), [
        //            'headers' => [
        //                'Content-Type' => 'application/json',
        //            ],
        //            'json' => [
        //                'blocks' => [
        //                    [
        //                        'type' => 'section',
        //                        'text' => [
        //                            'type' => 'mrkdwn',
        //                            'text' => 'Test',
        //                        ]
        //                    ]
        //                ]
        //            ],
        //        ]);

        $driverKey = ''; //  or ''

        $re = (new Client)->post('https://api.onesignal.com/notifications?c=push', [
            'headers' => [
                'Authorization' => 'Key '.config('services.onesignal.'.$driverKey.'api_key'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'app_id' => config('services.onesignal.'.$driverKey.'app_id'),
                'contents' => [
                    'en' => 'Please be ready',
                ],
                'headings' => [
                    'en' => 'Your bus has arrived',
                ],
                'include_aliases' => [
                    'external_id' => [
                        'User.2',
                    ],
                ],
                'target_channel' => 'push',
                'big_picture' => 'https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/pin-s+307EF3(-7.6158552,33.5511799)/-7.6158552,33.5511799,17,0,0/300x150?access_token=pk.eyJ1Ijoidm9vbHQtYWRtaW4iLCJhIjoiY20yNXlna2N5MHJ4bjJpc2psbDAyMHN0ZyJ9.dJ6I4BRtHoOVVVjjJlCLRQ',
            ],
        ]);

        dd($re->getBody()->getContents());

        return 0;
    }
}
