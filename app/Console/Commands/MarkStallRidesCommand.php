<?php

namespace App\Console\Commands;

use App\Enums\Transport\RideStatus;
use App\Models\Transport\Ride;
use Illuminate\Console\Command;

class MarkStallRidesCommand extends Command
{
    protected $signature = 'ride:mark-stall';

    protected $description = 'Mark long ongoing rides as stall';

    public function handle(): void
    {
        Ride::where('status', 'ongoing')
            ->where('started_at', '<', now()->subHours(4))
            ->where('status', RideStatus::Ongoing)
            ->update([
                'status' => RideStatus::Stalled,
            ]);
    }
}
