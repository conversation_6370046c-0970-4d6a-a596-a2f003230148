<?php

namespace App\Console\Commands;

use Database\Seeders\SalesDemoSeeder;
use Illuminate\Console\Command;

class SeedSalesDemoCommand extends Command
{
    protected $signature = 'sales:demo-seed';

    protected $description = 'Seed sales demo data with a subcompany and all related entities';

    public function handle()
    {
        if (app()->isProduction()) {
            return;
        }

        $this->info('Starting to seed sales demo data...');

        $this->call('db:seed', [
            '--class' => SalesDemoSeeder::class,
        ]);

        $this->info('Sales demo data seeding completed successfully!');
        $this->info('You can access the sales demo company using the super admin account.');

        return Command::SUCCESS;
    }
}
