<?php

namespace App\Console\Commands;

use App\Enums\General\Country;
use App\Enums\General\Models;
use App\Enums\Plan\ScheduleFrequency;
use App\Enums\Transport\RideMode;
use App\Models\Auth\User;
use App\Models\Fleet\Driver;
use App\Models\Geo\Location;
use App\Models\Plan\Plan;
use App\Models\Plan\Schedule;
use App\Models\Transport\Passenger;
use App\Models\Transport\PassengerResponsible;
use App\Models\Transport\Responsible;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Models\Transport\StationPassenger;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DemoDataSeedCommand extends Command
{
    protected $signature = 'demo:seed
                            {--munich : Seed Munich test data}
                            {--dubai : Seed Dubai test data}
                            {--casablanca : Seed Casablanca test data}';

    protected $description = 'Seed demo data for specified locations';

    private int $phoneCounter = 20;

    private int $emailCounter = 3;

    public function handle()
    {
        if (app()->isProduction()) {
            return;
        }

        $locations = array_filter([
            'munich' => $this->option('munich'),
            'dubai' => $this->option('dubai'),
            'casablanca' => $this->option('casablanca'),
        ]);

        if (empty($locations)) {
            $this->error('Please specify at least one location: --munich, --dubai, or --casablanca');

            return Command::FAILURE;
        }

        foreach (array_keys($locations) as $location) {
            $this->info("Seeding {$location} test data...");
            $this->seedData($location);
        }

        $this->info('Demo data seeding completed.');

        return Command::SUCCESS;
    }

    private function seedData(string $location): void
    {
        DB::transaction(function () use ($location) {
            $config = require database_path('seeders/Data/'.$location.'.php');
            $companyId = 1;
            $creator = User::where('company_id', $companyId)->first();

            $responsible = $this->createResponsible($config['responsible'], $companyId);
            $passengers = $this->createPassengers($config['passengers'], $responsible, $companyId, $creator);
            $driver = $this->createDriver($config['driver'], $companyId);
            $plan = $this->createPlan($config['plan'], $companyId, $driver->id);

            $routes = $this->createRoutes($config['routes'], $config['locations'], $passengers, $companyId, $creator->id);
            $this->createSchedules($config['schedules'], $routes, $plan, $companyId);
        });
    }

    private function createResponsible(array $responsibleConfig, int $companyId): Responsible
    {
        return Responsible::firstOrCreate([
            'email' => $responsibleConfig['email'],
            'phone' => $responsibleConfig['phone'],
            'company_id' => $companyId,
        ], [
            'first_name' => $responsibleConfig['first_name'],
            'last_name' => $responsibleConfig['last_name'],
            'region_id' => 1,
        ]);
    }

    private function createPassengers(array $passengersConfig, Responsible $responsible, int $companyId, User $creator): Collection
    {
        $passengers = collect();
        foreach ($passengersConfig as $passengerData) {
            $email = isset($passengerData['email_prefix'])
                ? strtolower($passengerData['email_prefix']).'@yosr.io'
                : $this->generateUniqueEmail(
                    strtolower(substr($passengerData['first_name'], 0, 1).$passengerData['last_name'])
                );

            $phone = isset($passengerData['phone_start'])
                ? '212'.$passengerData['phone_start']
                : $this->generateUniquePhone();

            $passenger = Passenger::firstOrCreate([
                'first_name' => $passengerData['first_name'],
                'last_name' => $passengerData['last_name'],
                'email' => $email,
                'phone' => $phone,
                'region_id' => 1,
                'group_id' => 1,
                'company_id' => $companyId,
            ]);

            if (isset($passengerData['location']) && $passengerData['location']) {
                Location::firstOrCreate(
                    [
                        'lat' => $passengerData['location']['lat'],
                        'lng' => $passengerData['location']['lng'],
                        'company_id' => $companyId,
                        'model_type' => Models::Passenger,
                        'model_id' => $passenger->id,
                    ],
                    [
                        'name' => $passengerData['location']['name'],
                        'primary' => true,
                        'street_line_1' => $passengerData['location']['name'],
                        'creator_type' => Models::User,
                        'creator_id' => $creator->id,
                    ]
                );
            }

            PassengerResponsible::firstOrCreate([
                'passenger_id' => $passenger->id,
                'responsible_id' => $responsible->id,
            ], [
                'company_id' => $companyId,
            ]);

            $passengers->push($passenger);
        }

        return $passengers;
    }

    private function createDriver(array $driverConfig, int $companyId): Driver
    {
        $email = isset($driverConfig['email_prefix'])
            ? $this->generateUniqueEmail($driverConfig['email_prefix'])
            : $this->generateUniqueEmail(
                strtolower(substr($driverConfig['first_name'], 0, 1).$driverConfig['last_name'])
            );

        $phone = isset($driverConfig['phone_start'])
            ? '212'.$driverConfig['phone_start']
            : $this->generateUniquePhone();

        return Driver::firstOrCreate([
            'first_name' => $driverConfig['first_name'],
            'last_name' => $driverConfig['last_name'],
            'email' => $email,
            'phone' => $phone,
            'region_id' => 1,
            'company_id' => $companyId,
        ]);
    }

    private function createPlan(array $planConfig, int $companyId, int $driverId): Plan
    {
        return Plan::firstOrCreate([
            'name' => $planConfig['name'],
            'driver_id' => $driverId,
            'company_id' => $companyId,
        ], [
            'status' => $planConfig['status'],
            'color' => $planConfig['color'] ?? null,
            'description' => $planConfig['description'] ?? null,
        ]);
    }

    private function createRoutes(array $routesConfig, array $locations, $passengers, int $companyId, int $creatorId): array
    {
        $createdRoutes = [];
        foreach ($routesConfig as $routeConfig) {
            $origin = $this->createLocation(
                $routeConfig['origin'],
                $locations[$routeConfig['origin']],
                $companyId,
                $creatorId
            );
            $destination = $this->createLocation(
                $routeConfig['destination'],
                $locations[$routeConfig['destination']],
                $companyId,
                $creatorId
            );

            $route = new Route([
                'name' => $routeConfig['name'],
                'reference' => \Illuminate\Support\Str::random(6),
                'status' => 'active',
                'static' => false,
                'optimized_at' => now()->subDays(rand(10, 100)),
                'origin_id' => $origin->id,
                'destination_id' => $destination->id,
                'company_id' => $companyId,
                'created_by' => $creatorId,
            ]);
            $route->save();

            Station::where('route_id', $route->id)->delete();
            $this->createStationsForRoute($route, $routeConfig['stops'], $locations, $passengers, $companyId, $creatorId);

            $createdRoutes[$routeConfig['name']] = $route;
        }

        return $createdRoutes;
    }

    private function createLocation(string $name, array $coordinates, int $companyId, int $creatorId): Location
    {
        return Location::create([
            'name' => $name,
            'street_line_1' => $name,
            'lat' => $coordinates['lat'],
            'lng' => $coordinates['lng'],
            'company_id' => $companyId,
            'creator_type' => Models::User,
            'creator_id' => $creatorId,
            'country' => Country::MA,
        ]);
    }

    private function createStationsForRoute(Route $route, array $stops, array $locations, $passengers, int $companyId, int $creatorId): void
    {
        $route->stationPassengers()->delete();
        $route->stations()->delete();

        foreach ($stops as $index => $stopName) {
            $location = $this->createLocation(
                $stopName,
                $locations[$stopName],
                $companyId,
                $creatorId
            );

            $station = new Station([
                'route_id' => $route->id,
                'location_id' => $location->id,
                'order' => $index + 1,
                'company_id' => $companyId,
                'created_by' => $creatorId,
            ]);
            $station->save();

            StationPassenger::where('station_id', $station->id)->delete();

            $stationPassenger = new StationPassenger([
                'station_id' => $station->id,
                'passenger_id' => $passengers[$index % $passengers->count()]->id,
                'route_id' => $route->id,
                'company_id' => $companyId,
                'seat' => 'Seat '.($index + 1),
            ]);
            $stationPassenger->save();
        }
    }

    private function createSchedules(array $schedulesConfig, array $routes, Plan $plan, int $companyId): void
    {
        $vehicle = \App\Models\Fleet\Vehicle::where('company_id', $companyId)->first();
        if (! $vehicle) {
            $vehicle = \App\Models\Fleet\Vehicle::factory()->create(['company_id' => $companyId]);
        }

        foreach ($schedulesConfig as $scheduleConfig) {
            $schedule = new Schedule([
                'plan_id' => $plan->id,
                'name' => $scheduleConfig['name'],
                'route_id' => $routes[$scheduleConfig['route_name']]->id,
                'ride_mode' => RideMode::PickUp,
                'start_date' => Carbon::parse($scheduleConfig['start_date'])->toDateString(),
                'end_date' => Carbon::parse($scheduleConfig['end_date'])->toDateString(),
                'start_time' => Carbon::createFromTimeString($scheduleConfig['start_time'])->toTimeString(),
                'arrival_time' => Carbon::createFromTimeString($scheduleConfig['arrival_time'])->toTimeString(),
                'frequency' => ScheduleFrequency::Daily,
                'color' => $scheduleConfig['color'],
                'days' => [],
                'company_id' => $companyId,
                'vehicle_id' => $vehicle->id,
            ]);
            $schedule->save();
        }
    }

    private function generateUniqueEmail(string $prefix): string
    {
        return $prefix.$this->emailCounter++.'@yosr.io';
    }

    private function generateUniquePhone(): string
    {
        return '212'.$this->phoneCounter++;
    }
}
