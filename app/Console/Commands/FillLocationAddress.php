<?php

namespace App\Console\Commands;

use App\Models\Geo\Location;
use App\Services\Geo\LocationService;
use Illuminate\Console\Command;

class FillLocationAddress extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'address:fill';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill missing location addresses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $locationService = app(LocationService::class);
        Location::get()->each(function (Location $location) use ($locationService) {
            if ($location->street_line_1) {
                return;
            }

            $data = $locationService->fillAddressFields([
                'company_id' => $location->company_id,
                'lat' => $location->lat,
                'lng' => $location->lng,
            ]);

            $location->update($data);
        });
    }
}
