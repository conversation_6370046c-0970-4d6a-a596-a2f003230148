<?php

namespace App\Console\Commands;

use App\Enums\Auth\UserStatus;
use App\Enums\General\Locale;
use App\Models\Auth\User;
use App\Models\Company\Company;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class ProductionDataSetupCommand extends Command
{
    protected $signature = 'prod:setup';

    protected $description = 'Setup Production Data';

    public function handle(): void
    {
        Company::factory()->create([
            'name' => 'Yosr Ltd',
            'locale' => Locale::English,
        ]);

        User::factory()->create([
            'first_name' => 'Super',
            'last_name' => 'Admin 1',
            'email' => '<EMAIL>',
            'status' => UserStatus::Active,
            'password' => Hash::make('G7^mP@3dLz*9vT!k'),
            'phone' => '212612345678',
            'company_id' => 1,
        ]);

        User::factory()->create([
            'first_name' => 'Super',
            'last_name' => 'Admin 1',
            'email' => '<EMAIL>',
            'status' => UserStatus::Active,
            'password' => Hash::make('G7^mP@3dLz*9vT!k'),
            'phone' => '212712345678',
            'company_id' => 1,
        ]);
    }
}
