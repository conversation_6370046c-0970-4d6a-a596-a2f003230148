<?php

namespace App\Notifications\Channels;

use App\Enums\General\Models;
use App\Mappers\Notification\OneSignalContentMapper;
use App\Services\Api\Notification\OneSignalApiService;
use App\Support\NotificationMessage;
use Illuminate\Notifications\Notification;

class OneSignalNotificationChannel
{
    public function send(object $notifiable, Notification $notification): void
    {
        $oneSignalApiService = app(OneSignalApiService::class);
        $oneSignalContentMapper = app(OneSignalContentMapper::class);

        /** @var NotificationMessage $notificationMessage */
        $notificationMessage = $notification->toMessage($notifiable);

        $oneSignalApiService->pushMobileNotification(
            $oneSignalContentMapper->getExternalIdsFor(Models::Responsible, collect([$notifiable->id]))->toArray(),
            $oneSignalContentMapper->getHeadings($notificationMessage),
            $oneSignalContentMapper->getContents($notificationMessage),
            $notificationMessage->getImage(),
            $notificationMessage->getRequestId()
        );
    }
}
