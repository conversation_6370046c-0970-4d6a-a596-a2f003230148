<?php

namespace App\Http\Resources\Transport;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RideItemResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $duration = isset($this->arrived_at) ? $this->arrived_at->diffInMinutes($this->started_at, true) : null;

        return [
            'id' => $this->id,
            'status' => $this->status,
            'distance' => $this->distance,
            'mode' => $this->mode,
            'duration' => intval($duration),
            'started_at' => $this->started_at,
            'arrived_at' => $this->arrived_at,
            'company_id' => $this->company_id,
            'route_name' => $this->whenLoaded('route', $this->route->name),
            'driver' => $this->whenLoaded('driver', [
                'name' => $this->driver->name,
                'image' => $this->driver->image,
            ]),
            'assistant' => $this->whenLoaded('assistant', [
                'name' => $this->assistant->name,
                'image' => $this->assistant->image,
            ]),
            'vehicle' => $this->whenLoaded('vehicle', [
                'name' => $this->vehicle->name,
                'plate' => $this->vehicle->plate,
            ]),
            'stops_count' => $this->whenCounted('stops'),
            'passengers_count' => $this->whenCounted('presentPassengers'),
        ];
    }
}
