<?php

namespace App\Http\Resources\Plan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceItemResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this['id'],
            'status' => $this['status'],
            'date' => $this['date'],
            'pickup_time' => $this['pickup_time'],
            'arrival_time' => $this['arrival_time'],
            'destination' => $this['destination'],
            'driver' => [
                'name' => $this['driver']['name'],
                'image' => $this['driver']['image'],
            ],
            'vehicle' => [
                'name' => $this['vehicle']['name'],
                'plate' => $this['vehicle']['plate'],
            ],
        ];
    }
}
