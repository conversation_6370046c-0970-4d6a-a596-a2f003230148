<?php

namespace App\Http\Requests\Import;

use App\Enums\Import\ImportType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UploadImportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Assuming all authenticated users can upload
    }

    public function rules(): array
    {
        return [
            'import_type' => [
                'required',
                'string',
                Rule::in(ImportType::getValues()),
            ],
            'temporary_media_ids' => [
                'required',
                'array',
            ],
            'temporary_media_ids.*' => [
                'integer',
            ],
        ];
    }
}
