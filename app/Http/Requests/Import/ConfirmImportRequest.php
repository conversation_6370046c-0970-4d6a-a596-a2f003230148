<?php

namespace App\Http\Requests\Import;

use App\Enums\Import\ImportType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConfirmImportRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'import_type' => [
                'required',
                'string',
                Rule::in(ImportType::getValues()),
            ],
            'temporary_media_ids' => [
                'required',
                'array',
            ],
            'temporary_media_ids.*' => [
                'integer',
            ],
            'matches' => [
                'required',
                'array',
            ],
            'matches.*.column' => [
                'required',
                'string',
            ],
            'matches.*.attribute_id' => [
                'nullable',
                'string',
            ],
        ];
    }
}
