<?php

namespace App\Http\Requests\Plan;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class UpdateScheduleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'nullable',
                'string',
            ],
            'date' => ! $this->route('schedule')->frequency ? ['required', 'date'] : [],
            'end_date' => $this->route('schedule')->frequency ? ['required', 'date'] : [],
            'arrival_time' => [
                'required_with:start_time',
                'nullable',
                'array',
            ],
            'arrival_time.hours' => [
                'required_with:arrival_time',
                'integer',
                'max:23',
                'min:0',
            ],
            'arrival_time.minutes' => [
                'required_with:arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'arrival_time.seconds' => [
                'required_with:arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'color' => [
                'nullable',
                'string',
                'max:15',
            ],
            'vehicle_id' => [
                'required',
                'integer',
                'exists:vehicles,id',
            ],
        ];
    }

    public function getAll(): array
    {
        $data = $this->all();

        $schedule = $this->route('schedule');

        if ((bool) $schedule->frequency && data_get($data, 'end_date') && $schedule->start_date->unix() >= Carbon::make($data['end_date'])->unix()) {
            throw ValidationException::withMessages([
                'end_date' => __('validation.after', [
                    'attribute' => __('general.end_date'),
                    'date' => __('general.start_date'),
                ]),
            ]);
        }

        if (data_get($data, 'start_time')) { // No need to check arrival_time
            $data['start_time'] = now()
                ->setHour($data['start_time']['hours'])
                ->setMinute($data['start_time']['minutes'])
                ->setSecond($data['start_time']['seconds']);

            $data['arrival_time'] = now()
                ->setHour($data['arrival_time']['hours'])
                ->setMinute($data['arrival_time']['minutes'])
                ->setSecond($data['arrival_time']['seconds']);

            if ($data['start_time']->unix() >= $data['arrival_time']->unix()) {
                throw ValidationException::withMessages([
                    'arrival_time' => __('validation.after', [
                        'attribute' => __('general.arrival_time'),
                        'date' => __('general.start_time'),
                    ]),
                ]);
            }

            $data['start_time'] = $data['start_time']->format('H:i:s');
            $data['arrival_time'] = $data['arrival_time']->format('H:i:s');
        }

        if ((bool) $schedule->frequency) {
            $data['date'] = null;
        } else {
            $data['start_date'] = $data['end_date'] = $data['date'];
        }

        return $data;
    }
}
