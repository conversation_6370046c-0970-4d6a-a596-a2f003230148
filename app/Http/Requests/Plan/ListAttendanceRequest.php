<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\AttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListAttendanceRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
                'max:50',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(AttendanceStatus::getValues()),
            ],
            'passenger_id' => [
                'nullable',
                'integer',
                'exists:passengers,id',
            ],
            'from' => [
                'nullable',
                'date',
            ],
            'to' => [
                'nullable',
                'date',
                'after_or_equal:from',
            ],
        ];
    }
}
