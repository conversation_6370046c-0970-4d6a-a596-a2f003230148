<?php

namespace App\Http\Requests\Plan;

use Illuminate\Foundation\Http\FormRequest;

class PlanRoutesRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'all_passengers' => [
                'required',
                'boolean',
            ],
            'passengers' => [
                'array',
                'required_if:all_passengers,false',
            ],
            'passengers.*' => [
                'integer',
                'required_if:all_passengers,false',
            ],
            'vehicles' => [
                'required',
                'array',
            ],
            'vehicles.*' => [
                'required',
                'integer',
            ],
            'origin_id' => [
                'required',
                'integer',
            ],
            'destination_id' => [
                'required',
                'integer',
            ],
            'drivers' => [
                'required',
                'array',
            ],
            'drivers.*' => [
                'required',
                'integer',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('general.name'),
        ];
    }
}
