<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\AbsenceFor;
use App\Enums\Plan\AbsenceReason;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAbsenceRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'passenger_id' => [
                'required',
                'integer',
                'exists:passengers,id',
            ],
            'from' => [
                'required',
                'date',
            ],
            'to' => [
                'required',
                'date',
                'after_or_equal:from',
            ],
            'note' => [
                'nullable',
                'string',
            ],
            'reason' => [
                'required',
                'string',
                Rule::in(AbsenceReason::getValues()),
            ],
            'for' => [
                'required',
                'string',
                Rule::in(array_merge(
                    AbsenceFor::getValues(),
                    ['transport,institute']
                )),
            ],
        ];
    }
}
