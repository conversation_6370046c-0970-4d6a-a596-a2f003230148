<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\AbsenceFor;
use App\Enums\Plan\AbsenceReason;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListAbsenceRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
                'max:50',
            ],
            'passenger_id' => [
                'nullable',
                'integer',
                'exists:passengers,id',
            ],
            'from' => [
                'nullable',
                'date',
            ],
            'to' => [
                'nullable',
                'date',
                'after_or_equal:from',
            ],
            'reason' => [
                'nullable',
                'string',
                Rule::in(AbsenceReason::getValues()),
            ],
            'for' => [
                'nullable',
                'string',
                Rule::in(array_merge(
                    AbsenceFor::getValues(),
                    ['transport,institute']
                )),
            ],
        ];
    }
}
