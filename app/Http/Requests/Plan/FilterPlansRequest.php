<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\PlanStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FilterPlansRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
                'max:50',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(PlanStatus::getValues()),
            ],
            'driver_id' => [
                'nullable',
                'integer',
                'exists:drivers,id',
            ],
        ];
    }
}
