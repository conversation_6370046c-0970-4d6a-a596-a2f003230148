<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\ScheduleFrequency;
use App\Enums\Transport\RideMode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRouteScheduleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'routes' => [
                'required',
                'array',
            ],
            'routes.*.route_id' => [
                'required',
                'integer',
                'exists:routes,id',
            ],
            'routes.*.vehicle_id' => [
                'required',
                'integer',
                'exists:vehicles,id',
            ],
            'routes.*.driver_id' => [
                'required',
                'integer',
                'exists:drivers,id',
            ],
            'routes.*.schedules' => [
                'required',
                'array',
            ],
            'routes.*.schedules.*.name' => [
                'nullable',
                'string',
            ],
            'routes.*.schedules.*.ride_mode' => [
                'required',
                'string',
                Rule::in(RideMode::getValues()),
            ],
            'routes.*.schedules.*.date' => [
                'required_without:routes.*.schedules.*.frequency',
                'nullable',
                'date',
            ],
            'routes.*.schedules.*.start_date' => [
                'required_with:routes.*.schedules.*.frequency',
                'nullable',
                'date',
            ],
            'routes.*.schedules.*.end_date' => [
                'required_with:routes.*.schedules.*.start_date',
                'nullable',
                'date',
                'after:routes.*.schedules.*.start_date',
            ],
            'routes.*.schedules.*.arrival_time' => [
                'required',
                'array',
            ],
            'routes.*.schedules.*.arrival_time.hours' => [
                'required_with:routes.*.schedules.*.arrival_time',
                'integer',
                'max:23',
                'min:0',
            ],
            'routes.*.schedules.*.arrival_time.minutes' => [
                'required_with:routes.*.schedules.*.arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'routes.*.schedules.*.arrival_time.seconds' => [
                'required_with:routes.*.schedules.*.arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'routes.*.schedules.*.ride_mode' => [
                'required',
                Rule::in(RideMode::getValues()),
            ],
            'routes.*.schedules.*.frequency' => [
                'nullable',
                Rule::in(ScheduleFrequency::getValues()),
            ],
            'routes.*.schedules.*.days' => [
                'required_if:routes.*.schedules.*.frequency,monthly,weekly',
                'array',
            ],
            'routes.*.schedules.*.color' => [
                'nullable',
                'string',
                'max:15',
            ],
        ];
    }
}
