<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\PlanStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePlanRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(PlanStatus::getValues()),
            ],
            'driver_id' => [
                'required',
                'integer',
                'exists:drivers,id',
            ],
        ];
    }
}
