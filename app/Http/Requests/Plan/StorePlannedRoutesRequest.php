<?php

namespace App\Http\Requests\Plan;

use Illuminate\Foundation\Http\FormRequest;

class StorePlannedRoutesRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'routes' => [
                'required',
                'array',
            ],
            'routes.*.origin_id' => [
                'required',
                'integer',
                'exists:locations,id',
            ],
            'routes.*.destination_id' => [
                'required',
                'integer',
                'exists:locations,id',
            ],
            'routes.*.name' => [
                'required',
                'string',
                'max:255',
            ],
            'routes.*.passengers' => [
                'required',
                'array',
            ],
            'routes.*.passengers.*.id' => [
                'required',
                'integer',
                'exists:passengers,id',
            ],
            'routes.*.vehicle' => [
                'required',
                'array',
            ],
            'routes.*.driver' => [
                'required',
                'array',
            ],
        ];
    }
}
