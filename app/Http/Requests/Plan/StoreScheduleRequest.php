<?php

namespace App\Http\Requests\Plan;

use App\Enums\Plan\ScheduleFrequency;
use App\Enums\Transport\RideMode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreScheduleRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'nullable',
                'string',
            ],
            'plan_id' => [
                'required',
                'integer',
                'exists:plans,id',
            ],
            'route_id' => [
                'required',
                'integer',
                'exists:routes,id',
            ],
            'vehicle_id' => [
                'required',
                'integer',
                'exists:vehicles,id',
            ],
            'assistant_id' => [
                'nullable',
                'integer',
                'exists:assistants,id',
            ],
            'ride_mode' => [
                'required',
                'string',
                Rule::in(RideMode::getValues()),
            ],
            'date' => [
                'required_without:frequency',
                'nullable',
                'date',
            ],
            'start_date' => [
                'required_with:frequency',
                'nullable',
                'date',
            ],
            'end_date' => [
                'required_with:start_date',
                'nullable',
                'date',
                'after:start_date',
            ],
            'time_selection' => [
                'required',
                Rule::in(['arrival', 'start']),
            ],
            'arrival_time' => [
                'required_if:time_selection,arrival',
                'array',
            ],
            'arrival_time.hours' => [
                'required_with:arrival_time',
                'integer',
                'max:23',
                'min:0',
            ],
            'arrival_time.minutes' => [
                'required_with:arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'arrival_time.seconds' => [
                'required_with:arrival_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'start_time' => [
                'required_if:time_selection,start',
                'array',
            ],
            'start_time.hours' => [
                'required_with:start_time',
                'integer',
                'max:23',
                'min:0',
            ],
            'start_time.minutes' => [
                'required_with:start_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'start_time.seconds' => [
                'required_with:start_time',
                'integer',
                'max:59',
                'min:0',
            ],
            'frequency' => [
                'nullable',
                Rule::in(ScheduleFrequency::getValues()),
            ],
            'days' => [
                'required_if:frequency,monthly,weekly',
                'array',
            ],
            'color' => [
                'nullable',
                'string',
                'max:15',
            ],
        ];
    }

    public function getAll(): array
    {
        $data = $this->all();

        $selectedTime = $data['time_selection'] === 'arrival' ? 'arrival_time' : 'start_time';

        if (isset($data[$selectedTime])) {
            $data[$selectedTime] = now()
                ->setHour($data[$selectedTime]['hours'])
                ->setMinute($data[$selectedTime]['minutes'])
                ->setSecond($data[$selectedTime]['seconds'])
                ->format('H:i:s');
        }

        $unusedTime = $data['time_selection'] === 'arrival' ? 'start_time' : 'arrival_time';
        unset($data[$unusedTime]);

        if (isset($data['frequency'])) {
            $data['date'] = null;
        } else {
            $data['start_date'] = $data['end_date'] = $data['date'];
        }

        unset($data['time_selection']);

        return $data;
    }
}
