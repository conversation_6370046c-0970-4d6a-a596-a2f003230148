<?php

namespace App\Http\Requests\Transport;

use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRidesRequest extends FormRequest
{
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(RideStatus::getValues()),
            ],
            'mode' => [
                'nullable',
                'string',
                Rule::in(RideMode::getValues()),
            ],
            'driver_id' => [
                'nullable',
                'integer',
                'exists:drivers,id',
            ],
        ];
    }
}
