<?php

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class StoreRouteRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'name' => [
                'required',
                'string',
            ],
            'static' => [
                'required',
                'bool',
            ],
            'status' => [
                'required',
                'string',
            ],
            'origin_id' => [
                'required',
                'integer',
            ],
            'destination_id' => [
                'required',
                'integer',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('general.name'),
        ];
    }
}
