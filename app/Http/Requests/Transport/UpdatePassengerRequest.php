<?php

namespace App\Http\Requests\Transport;

use App\Enums\Auth\UserGender;
use App\Enums\Transport\PassengerStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePassengerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $passengerId = $this->passenger->id;

        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'first_name' => [
                'nullable',
                'string',
            ],
            'last_name' => [
                'nullable',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:passengers,email,'.$passengerId,
            ],
            'phone' => [
                'nullable',
                'string',
                'unique:passengers,phone,'.$passengerId,
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(PassengerStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'image' => [
                'nullable',
                'image',
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'group_id' => [
                'nullable',
                'integer',
                'exists:groups,id',
            ],
            'birthdate' => [
                'nullable',
                'date',
            ],
        ];
    }
}
