<?php

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class StoreStationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'nullable',
                'string',
            ],
            'location_id' => [
                'required',
                'integer',
            ],
            'passengers' => [
                'nullable',
                'array',
            ],
            'passengers.*.id' => [
                'required',
                'integer',
                'exists:passengers,id',
            ],
            'passengers.*.seat' => [
                'nullable',
                'string',
                'max:255',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('general.name'),
        ];
    }
}
