<?php

namespace App\Http\Requests\Transport;

use App\Enums\Auth\UserGender;
use App\Enums\Auth\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateResponsibleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $responsibleId = $this->responsible->id;

        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'first_name' => [
                'nullable',
                'string',
            ],
            'last_name' => [
                'nullable',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:responsibles,email,'.$responsibleId,
            ],
            'phone' => [
                'nullable',
                'string',
                'unique:responsibles,phone,'.$responsibleId,
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(UserStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'image' => [
                'nullable',
                'image',
            ],
            'birthdate' => [
                'nullable',
                'date',
            ],
        ];
    }
}
