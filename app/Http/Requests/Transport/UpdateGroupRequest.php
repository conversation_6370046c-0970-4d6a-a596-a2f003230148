<?php

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class UpdateGroupRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'name' => [
                'required',
                'string',
            ],
            'description' => [
                'nullable',
                'string',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'reference' => __('general.reference'),
            'name' => __('general.name'),
            'description' => __('general.description'),
        ];
    }
}
