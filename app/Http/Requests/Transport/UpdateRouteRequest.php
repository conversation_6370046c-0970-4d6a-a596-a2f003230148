<?php

namespace App\Http\Requests\Transport;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRouteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'name' => [
                'required',
                'string',
            ],
            'static' => [
                'required',
                'bool',
            ],
            'status' => [
                'required',
                'string',
            ],
            'origin_id' => [
                'required',
                'integer',
            ],
            'destination_id' => [
                'required',
                'integer',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('general.name'),
        ];
    }
}
