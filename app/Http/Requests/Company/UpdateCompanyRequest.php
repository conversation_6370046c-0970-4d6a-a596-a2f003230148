<?php

namespace App\Http\Requests\Company;

use App\Enums\Company\Industry;
use App\Enums\General\Timezone;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
                'exists:companies,id',
            ],
            'name' => [
                'required',
                'string',
            ],
            'reference' => [
                'nullable',
                'string',
            ],
            'industry' => [
                'nullable',
                'string',
                Rule::in(Industry::getValues()),
            ],
            'timezone' => [
                'nullable',
                'string',
                Rule::in(array_column(Timezone::asOptions(), 'id')),
            ],
            'emergency_contact_id' => [
                'nullable',
                'integer',
                'exists:users,id',
            ],
            'location_id' => [
                'nullable',
                'integer',
                'exists:locations,id',
            ],
            'parent_id' => [
                'nullable',
                'integer',
                'exists:companies,id',
            ],
            'locale' => [
                'nullable',
            ],
            'logo' => [
                'nullable',
                'image',
            ],
            'delete_logo' => [
                'nullable',
                'bool',
            ],
        ];
    }
}
