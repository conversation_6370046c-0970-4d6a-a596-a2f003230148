<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Auth\UserGender;
use App\Enums\Fleet\DriverStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListDriversRequest extends FormRequest
{
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(DriverStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'default_vehicle_id' => [
                'nullable',
                'integer',
                'exists:vehicles,id',
            ],
        ];
    }
}
