<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Auth\UserGender;
use App\Enums\Fleet\AssistantStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssistantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'first_name' => [
                'required',
                'string',
            ],
            'last_name' => [
                'required',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:assistants,email',
            ],
            'phone' => [
                'required',
                'string',
                'unique:assistants,phone',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(AssistantStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
        ];
    }
}
