<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Auth\UserGender;
use App\Enums\Fleet\DriverStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDriverRequest extends FormRequest
{
    public function rules(): array
    {
        $driverId = $this->driver->id;

        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'first_name' => [
                'nullable',
                'string',
            ],
            'last_name' => [
                'nullable',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:drivers,email,'.$driverId,
            ],
            'phone' => [
                'nullable',
                'string',
                'unique:drivers,phone,'.$driverId,
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(DriverStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'image' => [
                'nullable',
                'image',
            ],
            'default_vehicle_id' => [
                'nullable',
                'integer',
                'exists:vehicles,id',
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
        ];
    }
}
