<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Fleet\VehicleStatus;
use App\Enums\Fleet\VehicleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateVehicleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'reference' => [
                'nullable',
                'string',
            ],
            'name' => [
                'required',
                'string',
            ],
            'type' => [
                'required',
                'string',
                Rule::in(VehicleType::getValues()),
            ],
            'status' => [
                'required',
                'string',
                Rule::in(VehicleStatus::getValues()),
            ],
            'make' => [
                'nullable',
                'string',
            ],
            'plate' => [
                'nullable',
                'string',
            ],
            'capacity' => [
                'required',
                'integer',
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
        ];
    }
}
