<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Fleet\VehicleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListVehiclesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
            ],
            'type' => [
                'nullable',
                'string',
                Rule::in(VehicleType::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
        ];
    }
}
