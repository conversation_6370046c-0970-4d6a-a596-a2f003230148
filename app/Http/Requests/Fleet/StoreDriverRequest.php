<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Auth\UserGender;
use App\Enums\Fleet\DriverStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreDriverRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
            ],
            'last_name' => [
                'required',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:drivers,email',
            ],
            'phone' => [
                'required',
                'string',
                'unique:drivers,phone',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(DriverStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
            'default_vehicle_id' => [
                'nullable',
                'integer',
                'exists:vehicles,id',
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
        ];
    }
}
