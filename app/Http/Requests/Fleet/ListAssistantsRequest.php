<?php

namespace App\Http\Requests\Fleet;

use App\Enums\Auth\UserGender;
use App\Enums\Fleet\AssistantStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListAssistantsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(AssistantStatus::getValues()),
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::getValues()),
            ],
        ];
    }
}
