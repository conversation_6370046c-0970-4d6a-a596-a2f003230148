<?php

namespace App\Http\Requests\Geo;

use App\Enums\Transport\RouteStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ViewRouteMapFiltersRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(RouteStatus::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'static' => [
                'nullable',
                'boolean',
            ],
        ];
    }
}
