<?php

namespace App\Http\Requests\Geo;

use App\Enums\General\Country;
use App\Enums\General\ModelType;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'model_type' => [
                'nullable',
                Rule::in(
                    ModelType::getKeys()
                ),
            ],
            'model_id' => [
                'nullable',
                'integer',
            ],
            'name' => [
                'nullable',
                'string',
            ],
            'country' => [
                'nullable',
                'string',
                Rule::in(Country::getValues()),
            ],
            'street_line_1' => [
                'nullable',
                'string',
            ],
            'street_line_2' => [
                'nullable',
                'string',
            ],
            'city' => [
                'nullable',
                'string',
            ],
            'state' => [
                'nullable',
                'string',
            ],
            'postal_code' => [
                'nullable',
                'string',
            ],
            'lat' => [
                'required',
                'numeric',
                'between:-90,90',
            ],
            'lng' => [
                'required',
                'numeric',
                'between:-180,180',
            ],
            'primary' => [
                'nullable',
                'boolean',
            ],
        ];
    }
}
