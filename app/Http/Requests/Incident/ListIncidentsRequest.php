<?php

namespace App\Http\Requests\Incident;

use App\Enums\Incident\IncidentType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListIncidentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search' => [
                'nullable',
                'string',
            ],
            'type' => [
                'nullable',
                'string',
                Rule::in(IncidentType::getValues()),
            ],
        ];
    }
}
