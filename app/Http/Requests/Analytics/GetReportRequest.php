<?php

namespace App\Http\Requests\Analytics;

use Illuminate\Foundation\Http\FormRequest;

class GetReportRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'filters' => [
                'nullable',
                'array',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'filters' => __('general.filters'),
        ];
    }
}
