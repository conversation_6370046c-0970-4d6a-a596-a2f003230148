<?php

namespace App\Http\Requests\Api\Review;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreReviewApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'ride_id' => [
                'required',
                'integer',
                'exists:rides,id',
            ],
            'rating' => [
                'required',
                Rule::in([
                    1, 2, 3, 4, 5,
                ]),
            ],
            'comment' => [
                'nullable',
                'string',
            ],
        ];
    }
}
