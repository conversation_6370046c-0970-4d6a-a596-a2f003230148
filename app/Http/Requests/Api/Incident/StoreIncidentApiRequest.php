<?php

namespace App\Http\Requests\Api\Incident;

use Illuminate\Foundation\Http\FormRequest;

class StoreIncidentApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                'string',
            ],
            'description' => [
                'nullable',
                'string',
            ],
            'lat' => [
                'required',
                'numeric',
                'between:-90,90',
            ],
            'lng' => [
                'required',
                'numeric',
                'between:-180,180',
            ],
            'ride_id' => [
                'nullable',
                'integer',
                'exists:rides,id',
            ],
        ];
    }
}
