<?php

namespace App\Http\Requests\Api\Auth;

use App\Enums\General\MobileAppPackage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VerifyOtpApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'string',
            ],
            'otp' => [
                'required',
                'numeric',
            ],
            'package' => [
                'required',
                Rule::in(MobileAppPackage::getValues()),
            ],
        ];
    }
}
