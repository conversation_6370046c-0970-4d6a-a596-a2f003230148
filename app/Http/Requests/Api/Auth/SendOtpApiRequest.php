<?php

namespace App\Http\Requests\Api\Auth;

use App\Enums\General\MobileAppPackage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SendOtpApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'string',
            ],
            'package' => [
                'required',
                Rule::in(MobileAppPackage::getValues()),
            ],
        ];
    }
}
