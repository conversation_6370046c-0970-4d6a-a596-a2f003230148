<?php

namespace App\Http\Requests\Api\Geo;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLiveLocationApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'ride_id' => [
                'required',
                'integer',
                'exists:rides,id',
            ],
            'lng' => [
                'required',
                'numeric',
                'between:-180,180',
            ],
            'lat' => [
                'required',
                'numeric',
                'between:-90,90',
            ],
            'etas' => [
                'nullable',
                'array',
            ],
            'etas.*.stop_id' => [
                'required',
                'numeric',
            ],
            'etas.*.duration' => [
                'required',
                'numeric',
            ],
            'etas.*.distance' => [
                'required',
                'numeric',
            ],
        ];
    }
}
