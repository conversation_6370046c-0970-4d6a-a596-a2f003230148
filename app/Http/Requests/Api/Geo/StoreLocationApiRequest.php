<?php

namespace App\Http\Requests\Api\Geo;

use App\Enums\General\Models;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'street_line_1' => [
                'nullable',
                'string',
                'max:255',
            ],
            'street_line_2' => [
                'nullable',
                'string',
                'max:255',
            ],
            'city' => [
                'nullable',
                'string',
                'max:255',
            ],
            'state' => [
                'nullable',
                'string',
                'max:255',
            ],
            'postal_code' => [
                'nullable',
                'string',
                'max:255',
            ],
            'lat' => [
                'required',
                'numeric',
                'between:-90,90',
            ],
            'lng' => [
                'required',
                'numeric',
                'between:-180,180',
            ],
            'primary' => [
                'nullable',
                'boolean',
            ],
            'model_type' => [
                'required',
                'string',
                Rule::in([
                    Models::Passenger,
                ]),
            ],
            'model_id' => [
                'required',
                'integer',
            ],
        ];
    }
}
