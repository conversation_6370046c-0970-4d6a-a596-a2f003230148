<?php

namespace App\Http\Requests\Api\Plan;

use App\Enums\Plan\AttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAttendanceApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'status' => [
                'required',
                Rule::in([
                    AttendanceStatus::Absent,
                    AttendanceStatus::Canceled,
                ]),
            ],
            'date' => [
                'required',
                'date',
            ],
            'passenger_id' => [
                'required',
                'integer',
                'exists:passengers,id',
            ],
            'schedule_id' => [
                'required',
                'integer',
                'exists:schedules,id',
            ],
            'ride_id' => [
                'nullable',
                'integer',
                'exists:rides,id',
            ],
        ];
    }
}
