<?php

namespace App\Http\Requests\Api\Plan;

use App\Enums\Plan\AbsenceFor;
use App\Enums\Plan\AbsenceReason;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAbsenceApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'for' => [
                'required',
                'string',
                Rule::in(AbsenceFor::getValues()),
            ],
            'reason' => [
                'required',
                'string',
                Rule::in(AbsenceReason::getValues()),
            ],
            'from' => [
                'required',
                'date',
            ],
            'to' => [
                'required',
                'date',
            ],
            'note' => [
                'nullable',
                'string',
            ],
            'passenger_id' => [
                'required',
                'integer',
                'exists:passengers,id',
            ],
        ];
    }
}
