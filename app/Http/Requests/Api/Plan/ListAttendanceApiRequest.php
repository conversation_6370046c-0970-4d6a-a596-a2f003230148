<?php

namespace App\Http\Requests\Api\Plan;

use App\Enums\Plan\PeriodFilter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListAttendanceApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'passenger_id' => [
                'integer',
                'required',
                'exists:passengers,id',
            ],
            'period' => [
                'required',
                'string',
                Rule::in(PeriodFilter::getValues()),
            ],
        ];
    }
}
