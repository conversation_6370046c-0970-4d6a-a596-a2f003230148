<?php

namespace App\Http\Requests\Api\Transport\Ride;

use App\Enums\General\Models;
use App\Enums\General\ModelType;
use App\Models\Fleet\Assistant;
use App\Models\Transport\Passenger;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetOnGoingRideApiRequest extends FormRequest
{
    public function rules()
    {
        return [
            'user_type' => [
                'required',
                'string',
                Rule::in([
                    Models::Assistant,
                    Models::Passenger,
                ]),
            ],
            'user_id' => [
                'required',
                'integer',
            ],
        ];
    }

    public function getSubjectUser(): Passenger|Assistant
    {
        return ModelType::getValue($this->user_type)::findOrFail($this->user_id);
    }
}
