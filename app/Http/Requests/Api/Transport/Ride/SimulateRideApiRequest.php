<?php

namespace App\Http\Requests\Api\Transport\Ride;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SimulateRideApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'schedule_id' => [
                'required',
                'integer',
                'exists:schedules,id',
            ],
            'speed' => [
                'nullable',
                'string',
                Rule::in([
                    'slow',
                    'normal',
                    'fast',
                ]),
            ],
            'allow_absence' => [
                'nullable',
                'boolean',
            ],
            'allow_driver_stop_skip' => [
                'nullable',
                'boolean',
            ],
            'allow_passenger_stop_cancel' => [
                'nullable',
                'boolean',
            ],
            'allow_driver_cancel_ride' => [
                'nullable',
                'boolean',
            ],
        ];
    }
}
