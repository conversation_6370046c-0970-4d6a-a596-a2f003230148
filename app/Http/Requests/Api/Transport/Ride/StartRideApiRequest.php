<?php

namespace App\Http\Requests\Api\Transport\Ride;

use Illuminate\Foundation\Http\FormRequest;

class StartRideApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'schedule_id' => [
                'required',
                'exists:schedules,id',
            ],
            'start_lat' => [
                'required',
                'numeric',
            ],
            'start_lng' => [
                'required',
                'numeric',
            ],
            'stations' => [
                'required',
                'array',
            ],
            'stations.*' => [
                'integer',
                'required',
            ],
            'duration' => [
                'integer',
                'nullable',
            ],
        ];
    }
}
