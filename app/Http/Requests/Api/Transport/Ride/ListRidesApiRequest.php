<?php

namespace App\Http\Requests\Api\Transport\Ride;

use App\Enums\General\Models;
use App\Enums\General\ModelType;
use App\Enums\Transport\RideMode;
use App\Enums\Transport\RideStatus;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListRidesApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'user_type' => [
                'required',
                'string',
                Rule::in([
                    Models::Driver,
                    Models::Assistant,
                ]),
            ],
            'user_id' => [
                'required',
                'integer',
            ],
            'day' => [
                'required',
                'date',
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(RideStatus::getValues()),
            ],
            'mode' => [
                'nullable',
                'string',
                Rule::in(RideMode::getValues()),
            ],
        ];
    }

    public function getSubjectUser(): Passenger|Driver|Assistant
    {
        return ModelType::getValue($this->user_type)::findOrFail($this->user_id);
    }
}
