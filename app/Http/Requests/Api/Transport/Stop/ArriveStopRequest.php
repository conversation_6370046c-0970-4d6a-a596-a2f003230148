<?php

namespace App\Http\Requests\Api\Transport\Stop;

use Illuminate\Foundation\Http\FormRequest;

class ArriveStopRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

        ];
    }
}
