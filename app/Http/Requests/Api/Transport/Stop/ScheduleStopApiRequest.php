<?php

namespace App\Http\Requests\Api\Transport\Stop;

use Illuminate\Foundation\Http\FormRequest;

class ScheduleStopApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'station_id' => [
                'required',
                'integer',
                'exists:stations,id',
            ],
            'ride_id' => [
                'required',
                'integer',
                'exists:rides,id',
            ],
            'scheduled_at' => [
                'required',
                'date',
            ],
        ];
    }
}
