<?php

namespace App\Http\Requests\Api\Transport\Stop;

use Illuminate\Foundation\Http\FormRequest;

class CancelStopApiRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'reason' => [
                'nullable',
                'integer',
            ],
            'stop_id' => [
                'required',
                'integer',
                'exists:stops,id',
            ],
        ];
    }
}
