<?php

namespace App\Http\Requests\Api\Transport\Stop;

use Illuminate\Foundation\Http\FormRequest;

class SkipStopRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'reason' => [
                'nullable',
                'string',
            ],
            'station_id' => [
                'required',
                'integer',
                'exists:stations,id',
            ],
            'ride_id' => [
                'required',
                'integer',
                'exists:rides,id',
            ],
        ];
    }
}
