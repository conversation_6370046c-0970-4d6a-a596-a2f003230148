<?php

namespace App\Http\Requests\Auth;

use App\Enums\Auth\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $userId = $this->user->id;

        return [
            'first_name' => [
                'nullable',
                'string',
            ],
            'last_name' => [
                'nullable',
                'string',
            ],
            'email' => [
                'nullable',
                'string',
                'unique:users,email,'.$userId,
            ],
            'phone' => [
                'nullable',
                'string',
                'unique:users,phone,'.$userId,
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(UserStatus::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
            'image' => [
                'nullable',
                'image',
            ],
        ];
    }
}
