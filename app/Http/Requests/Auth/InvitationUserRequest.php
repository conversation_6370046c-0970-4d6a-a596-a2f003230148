<?php

namespace App\Http\Requests\Auth;

use App\Enums\Auth\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class InvitationUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
            ],
            'last_name' => [
                'required',
                'string',
            ],
            'email' => [
                'required',
                'email',
                'unique:users,email',
            ],
            'phone' => [
                'required',
                'string',
                'unique:users,phone',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(UserStatus::getValues()),
            ],
            'region_id' => [
                'nullable',
                'integer',
                'exists:regions,id',
            ],
        ];
    }
}
