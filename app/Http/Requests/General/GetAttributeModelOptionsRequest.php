<?php

namespace App\Http\Requests\General;

use App\Enums\General\EnumType;
use App\Enums\General\ModelType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Class GetAttributeModelOptionsRequest
 *
 *
 * @property string $model
 * @property int|null $model_id
 * @property string|null $search
 */
class GetAttributeModelOptionsRequest extends FormRequest
{
    public function rules()
    {
        return [
            'model' => [
                'required',
                Rule::in(
                    array_merge(
                        array_keys(ModelType::asArray()),
                        array_keys(EnumType::asArray())
                    )
                ),
            ],
            'model_id' => [
                'nullable',
                'integer',
            ],
            'search' => [
                'nullable',
                'string',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'model' => __('validation.attributes.model_type'),
            'model_id' => __('validation.attributes.model_id'),
            'search' => __('general.search.normal'),
        ];
    }
}
