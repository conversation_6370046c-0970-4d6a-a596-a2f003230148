<?php

namespace App\Http\Requests\General;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreRegionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
            ],
            'emergency_contact_id' => [
                'nullable',
                'integer',
                'exists:users,id',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('general.name'),
            'emergency_contact_id' => __('general.emergency_contact'),
        ];
    }
}
