<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;

class LocaleController extends Controller
{
    private array $supportedLanguages = [
        'en', 'fr', 'ar',
    ];

    public function setLocale(string $locale): RedirectResponse
    {
        $locale = $this->localeIsSupported($locale) ? $locale : 'en';

        if (auth()->check()) {
            auth()->user()->update([
                'locale' => $locale,
            ]);
        }

        cookie()->forget('locale', $locale);

        return back();
    }

    public function localeIsSupported(string $locale): bool
    {
        return in_array($locale, $this->supportedLanguages);
    }
}
