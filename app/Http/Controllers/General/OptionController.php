<?php

namespace App\Http\Controllers\General;

use App\Enums\Auth\UserStatus;
use App\Enums\General\EnumType;
use App\Enums\General\Models;
use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\General\GetAttributeModelOptionsRequest;
use App\Models\Auth\User;
use App\Models\Geo\Location;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Traits\General\HasCompany;
use BenSampo\Enum\Exceptions\InvalidEnumKeyException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;

class OptionController extends Controller
{
    /**
     * @throws InvalidEnumKeyException
     */
    public function list(GetAttributeModelOptionsRequest $request): JsonResponse
    {
        if (EnumType::hasKey($request->model)) {
            return $this->handleEnumType($request);
        }

        $modelType = ModelType::fromKey($request->model)->value;
        $query = $this->buildQuery($modelType, $request);

        $this->applySpecialConditions($query, $modelType, $request);

        if ($request->has('filters')) {
            $this->applyFilters($query, $modelType, $request);
        }

        if ($request->has('search')) {
            $this->applySearch($query, $modelType, $request);
        }

        return response()->json($query->get());
    }

    private function handleEnumType(GetAttributeModelOptionsRequest $request): JsonResponse
    {
        $enumClassname = EnumType::fromKey($request->model)->value;
        $options = $enumClassname::asOptions();

        if ($request->has('search')) {
            $searchTerm = strtolower($request->get('search'));
            $options = array_filter($options, function ($option) use ($searchTerm) {
                return strpos(strtolower($option['name']), $searchTerm) !== false;
            });
        }

        return response()->json(array_values($options));
    }

    private function buildQuery(string $modelType, GetAttributeModelOptionsRequest $request): Builder
    {
        $columns = $this->getColumns($modelType);
        $query = $modelType::select($columns);

        $query->when(isset($request->except_ids), fn ($q) => $q->whereNotIn('id', $request->except_ids))
            ->when(isset($request->relation_column), fn ($q) => $q->where($request->relation_column, intval($request->relation_id)));

        if (in_array(HasCompany::class, class_uses($modelType))) {
            $query->company(auth()->user()->company_id);
        }

        if ($request->has('limit')) {
            $query->limit($request->get('limit'));
        }

        return $query;
    }

    private function getColumns(string $modelType): array
    {
        return match ($modelType) {
            ModelType::User, ModelType::Passenger, ModelType::Driver, ModelType::Assistant, ModelType::Responsible => ['id', 'first_name', 'last_name'],
            ModelType::Vehicle => ['id', 'name', 'type'],
            ModelType::Location => ['id', 'street_line_1', 'name', 'lat', 'lng'],
            default => ['id', 'name']
        };
    }

    private function applySpecialConditions(Builder $query, string $modelType, GetAttributeModelOptionsRequest $request): void
    {
        if ($request->model === Models::User) {
            $query->whereStatus(UserStatus::Active);
        }

        if ($request->missed_ids) {
            $query->whereIn('id', $request->collect('missed_ids'));
        }

        if ($request->model_id) {
            $query->where('id', $request->model_id);
        }
    }

    private function applyFilters(Builder $query, string $modelType, GetAttributeModelOptionsRequest $request): void
    {
        if (in_array($modelType, [ModelType::Location, ModelType::User, ModelType::Route])) {
            $query->filter($request->filters);

            return;
        }

        foreach ($request->filters as $key => $value) {
            $query = is_array($value) ? $query->whereIn($key, $value) : $query->where($key, $value);
        }
    }

    private function applySearch(Builder $query, string $modelType, GetAttributeModelOptionsRequest $request): void
    {
        $searchTerm = '%'.$request->get('search').'%';

        if ($modelType === ModelType::User || $modelType === ModelType::Passenger) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'ilike', $searchTerm)
                    ->orWhere('last_name', 'ilike', $searchTerm);
            });

            if (isset($request->except_ids)) {
                $query->whereNotIn('id', $request->except_ids);
            }

            $query->limit(10);

            return;
        }

        if ($modelType === ModelType::Location) {
            $query->filter(['search' => $request->get('search')]);

            if (isset($request->except_ids)) {
                $query->whereNotIn('id', $request->except_ids);
            }

            return;
        }

        $searchColumn = $this->getSearchColumn($modelType);
        $query->where($searchColumn, 'ilike', $searchTerm);

        if (isset($request->except_ids)) {
            $query->whereNotIn('id', $request->except_ids);
        }
    }

    private function getSearchColumn(string $modelType): string
    {
        return in_array($modelType, [
            ModelType::User,
            ModelType::Assistant,
            ModelType::Driver,
            ModelType::Passenger,
            ModelType::Responsible,
        ]) ? 'first_name' : 'name';
    }

    /**
     * @return string[]
     */
    public function hasAccessModels(): array
    {
        return [
            Route::class,
            Station::class,
            User::class,
            Location::class,
        ];
    }
}
