<?php

namespace App\Http\Controllers\General;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\General\StoreRegionRequest;
use App\Http\Requests\General\UpdateRegionRequest;
use App\Models\Company\Company;
use App\Models\General\Region;
use App\Services\General\RegionService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class RegionController extends Controller
{
    public function __construct(
        private readonly RegionService $regionService,
        // private readonly ModelDeletionCheckService $modelDeletionCheckService
    ) {}

    public function view(?Region $region = null): Response|RedirectResponse
    {
        $this->authorize('list', Region::class);

        if (! $region) {
            if ($region = Region::query()->company(auth()->user()->company_id)->first()) {
                $this->authorize('view', $region);

                return redirect()->route(request()->route()->getName(), $region->id);
            }
        }

        return Inertia::render('Settings/Transport/Regions/View', [
            'regions' => Region::query()->company(auth()->user()->company_id)->get(),
            'region' => $region?->load('emergencyContact'),
        ]);
    }

    public function save(StoreRegionRequest $request): RedirectResponse
    {
        $this->authorize('create', Region::class);

        $company = Company::findOrFail(auth()->user()->company_id);

        $region = $this->regionService->save($company, $request->validated());

        $this->notifyModelUpdate(ModelType::Region);

        return to_route('settings.transport.regions.view', $region->id);
    }

    public function edit(Region $region): Response
    {
        $this->authorize('update', $region);

        return Inertia::render('Settings/Transport/Regions/Edit', [
            'regions' => Region::query()->company(auth()->user()->company_id)
                ->get(),
            'region' => $region,
        ]);
    }

    public function update(UpdateRegionRequest $request, Region $region): RedirectResponse
    {
        $this->authorize('update', $region);

        $company = Company::findOrFail(auth()->user()->company_id);

        $this->regionService->update($company, $region, $request->validated());

        $this->notifyModelUpdate(ModelType::Region, 'update');

        return to_route('settings.transport.regions.view', $region->id);
    }

    public function delete(Region $region): RedirectResponse
    {
        $this->authorize('delete', $region);

        // if ($this->modelDeletionCheckService->canDelete(Region::class, $region->id)) {
        //     $this->addToast('transport.region.messages.cant_delete', 'general.deletion_error', 'error');

        //     return back();
        // }

        $this->regionService->delete($region);

        $this->notifyModelUpdate(ModelType::Region, 'delete');

        return to_route('settings.transport.regions.view');
    }
}
