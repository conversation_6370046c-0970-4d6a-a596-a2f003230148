<?php

namespace App\Http\Controllers\General;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\General\StoreTemporaryFileRequest;
use App\Models\Auth\User;
use App\Models\General\Media;
use Illuminate\Support\Facades\Storage;

class TemporaryMediaController extends Controller
{
    public function save(StoreTemporaryFileRequest $request)
    {
        $user = User::find(auth()->user()->id);

        $media = $user->addMediaFromRequest('file')->toMediaCollection('temp');

        return response()->json([
            'temporary_media_id' => $media->id,
        ]);
    }

    public function delete()
    {
        $user = User::find(auth()->user()->id);
        $user->clearMediaCollection('temp');

        $this->notifyModelUpdate(ModelType::Media, 'delete');
    }

    public function stream(Media $media)
    {
        return Storage::download($media->getPath());
    }
}
