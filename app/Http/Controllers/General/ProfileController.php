<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\UpdateImageRequest;
use App\Traits\General\AppUser;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    public function edit(Request $request): Response
    {
        return Inertia::render('Profile/Edit', [
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status' => session('status'),
        ]);
    }

    public function updateImage(UpdateImageRequest $request): RedirectResponse
    {
        /** @var AppUser $user */
        $user = auth()->user();

        $user->clearImage();

        $user->addMediaFromRequest('image')->toMediaCollection('image');

        // todo translations
        $this->addToast('Profile image updated');

        return back();
    }
}
