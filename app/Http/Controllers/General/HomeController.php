<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use App\Services\Analytics\Fleet\FleetAnalyticsService;
use App\Services\Analytics\Incident\IncidentAnalyticsService;
use App\Services\Analytics\Plan\PlanAnalyticsService;
use App\Services\Analytics\Transport\TransportAnalyticsService;
use App\Services\Fleet\AssistantService;
use App\Services\Fleet\DriverService;
use App\Services\Fleet\VehicleService;
use App\Services\Incident\IncidentService;
use App\Services\Plan\AbsenceService;
use App\Services\Review\ReviewService;
use App\Services\Transport\RideService;
use Illuminate\Contracts\Auth\Authenticatable;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __construct(
        private readonly RideService $rideService,
        private readonly IncidentService $incidentService,
        private readonly AbsenceService $absenceService,
        private readonly DriverService $driverService,
        private readonly VehicleService $vehicleService,
        private readonly AssistantService $assistantService,
        private readonly ReviewService $reviewService,
        private readonly TransportAnalyticsService $transportAnalyticsService,
        private readonly FleetAnalyticsService $fleetAnalyticsService,
        private readonly IncidentAnalyticsService $incidentAnalyticsService,
        private readonly PlanAnalyticsService $planAnalyticsService
    ) {}

    public function index(Authenticatable $user): Response
    {
        $currentDate = now();

        return Inertia::render('Home/Main', [
            'rides' => $this->rideService->getDashboardRidesDataForPeriod($user->company, $currentDate, $currentDate->copy()->addDays(5)),
            'today_incidents' => $this->incidentService->getIncidentsForDate($user->company, $currentDate, 5),
            'today_absences' => $this->absenceService->getAbsencesForDate($user->company, $currentDate),
            'top_drivers' => $this->driverService->getTopDrivers($user),
            'top_vehicles' => $this->vehicleService->getTopVehicles($user),
            'top_assistants' => $this->assistantService->getTopAssistants($user),
            'recent_reviews' => $this->reviewService->getRecentReviews($user),
            'dashboard_stats' => [
                'passengers_count' => $this->transportAnalyticsService->getPassengersCount($user),
                'drivers_count' => $this->fleetAnalyticsService->getDriversCount($user),
                'vehicles_count' => $this->fleetAnalyticsService->getVehiclesCount($user),
                'assistants_count' => $this->fleetAnalyticsService->getAssistantsCount($user),
                'incidents_today_count' => $this->incidentAnalyticsService->getIncidentsCountForDate($user->company, $currentDate),
                'absences_today_count' => $this->planAnalyticsService->getAbsencesCountForDate($user->company, $currentDate),
            ],
        ]);
    }

    public function personalSettings(): Response
    {
        return Inertia::render('Settings/Personal/Main');
    }
}
