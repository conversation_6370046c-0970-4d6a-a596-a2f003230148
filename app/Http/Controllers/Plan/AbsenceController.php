<?php

namespace App\Http\Controllers\Plan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\ListAbsenceRequest;
use App\Http\Requests\Plan\StoreAbsenceRequest;
use App\Http\Resources\Plan\AbsenceResource;
use App\Services\Plan\AbsenceService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;

class AbsenceController extends Controller
{
    public function __construct(
        protected AbsenceService $absenceService,
    ) {}

    public function list(ListAbsenceRequest $request): Response
    {
        return inertia('Plan/Absences/List/Main', [
            'absences' => AbsenceResource::collection(
                $this->absenceService->list(auth()->user(), $request->validated())
            ),
        ]);
    }

    public function save(StoreAbsenceRequest $request): RedirectResponse
    {
        $this->absenceService->store([
            'from' => Carbon::make($request->from),
            'to' => Carbon::make($request->to),
            'passenger_id' => $request->passenger_id,
            'for' => $request->for,
            'reason' => $request->reason,
            'note' => $request->note,
            'company_id' => auth()->user()->company_id,
            'creator_id' => auth()->user()->id,
            'creator_type' => auth()->user()->type(),
        ]);

        return to_route('plans.absences.list');
    }
}
