<?php

namespace App\Http\Controllers\Plan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\FilterPlansRequest;
use App\Http\Requests\Plan\StorePlanRequest;
use App\Http\Requests\Plan\UpdatePlanRequest;
use App\Http\Resources\Plan\PlanResource;
use App\Http\Resources\Plan\ScheduleWebResource;
use App\Models\Plan\Plan;
use App\Services\Plan\PlanService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response as InertiaResponse;

class PlanController extends Controller
{
    public function __construct(
        protected PlanService $planService
    ) {}

    public function list(FilterPlansRequest $request): InertiaResponse
    {
        $this->authorize('list', Plan::class);

        $filters = $request->validated();
        $plans = $this->planService->list(auth()->user(), $filters);

        return inertia('Plan/Plans/List/Main', [
            'plans' => PlanResource::collection($plans),
        ]);
    }

    public function store(StorePlanRequest $request): RedirectResponse
    {
        $this->authorize('create', Plan::class);

        $data = $request->validated();
        $data['company_id'] = auth()->user()->company_id;
        $plan = $this->planService->store($data);

        return redirect()->route('plans.view', $plan->id);
    }

    public function view(Plan $plan): InertiaResponse
    {
        $this->authorize('view', $plan);

        return inertia('Plan/Plans/View/Schedules', [
            'plan' => new PlanResource($plan),
            'schedules' => ScheduleWebResource::collection(
                $plan->load(['schedules.route:id,name', 'driver', 'schedules.vehicle:id'])->schedules
            ),
        ]);
    }

    public function update(UpdatePlanRequest $request, Plan $plan): RedirectResponse
    {
        $this->authorize('update', $plan);

        $this->planService->update($plan, $request->validated());

        return back();
    }

    public function delete(Plan $plan): RedirectResponse
    {
        $this->authorize('delete', $plan);

        $this->planService->delete($plan);

        return to_route('plans.list');
    }
}
