<?php

namespace App\Http\Controllers\Plan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\ListAttendanceRequest;
use App\Http\Resources\Plan\AttendanceResource;
use App\Services\Plan\AttendanceService;
use Inertia\Response;

class AttendanceController extends Controller
{
    public function __construct(
        protected AttendanceService $attendanceService
    ) {}

    public function list(ListAttendanceRequest $request): Response
    {
        return inertia('Plan/Attendances/List/Main', [
            'attendances' => AttendanceResource::collection(
                $this->attendanceService->list(
                    auth()->user(),
                    $request->validated()
                )
            ),
        ]);
    }
}
