<?php

namespace App\Http\Controllers\Plan;

use App\Enums\System\UserEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\PlanRoutesRequest;
use App\Http\Requests\Plan\StorePlannedRoutesRequest;
use App\Http\Requests\Plan\StoreRouteScheduleRequest;
use App\Http\Requests\Transport\ListPassengersRequest;
use App\Services\Api\Geo\MapboxOptimizationApiService;
use App\Services\Fleet\DriverService;
use App\Services\Fleet\VehicleService;
use App\Services\Geo\LocationService;
use App\Services\Plan\RoutePlanService;
use App\Services\Transport\PassengerService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

use function Sentry\captureException;

class RoutePlanController extends Controller
{
    public function __construct(
        private readonly RoutePlanService $routePlanService,
        private readonly PassengerService $passengerService,
        private readonly MapboxOptimizationApiService $mapboxV2ApiService,
        private readonly LocationService $locationService,
        private readonly VehicleService $vehicleService,
        private readonly DriverService $driverService,
    ) {}

    public function show(): Response
    {
        return Inertia::render('Plan/RoutePlan/Main');
    }

    public function plan(PlanRoutesRequest $request): JsonResponse|RedirectResponse
    {
        if (! app()->isProduction()) {
            return response()->json([
                'routes' => $this->routePlanService->plan($request->validated()),
            ]);
        }
        $origin = $this->locationService->find($request->origin_id);
        $destination = $this->locationService->find($request->destination_id);

        $passengerLocations = $request->all_passengers ?
            $this->locationService->getForAllPassengers()
            : $this->locationService->getForPassengerIds($request->passengers);

        $vehicles = $this->vehicleService->findMany($request->vehicles);
        $drivers = $this->driverService->findMany($request->drivers);

        try {
            $routes = $this->mapboxV2ApiService->optimizeRoutes(
                $origin,
                $destination,
                $passengerLocations,
                $vehicles,
                $drivers
            );
        } catch (Exception $exception) {
            $this->addToast('messages.custom.cant_generate_routes_atm', type: 'error');

            captureException($exception);
        }

        Mixpanel::trackEvent(auth()->user(), UserEvent::RouteOptimized, [
            'company' => auth()->user()?->company?->name,
        ]);

        return response()->json([
            'routes' => $routes ?? [],
        ]);
    }

    public function store(StorePlannedRoutesRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $routes = $this->routePlanService->store($validated['routes']);

        return response()->json([
            'status' => 'success',
            'routes' => $routes,
        ]);
    }

    public function passengers(ListPassengersRequest $request): JsonResponse
    {
        $passengers = $this->passengerService->get(
            auth()->user(),
            filterData: array_merge($request->validated()),
            paginate: true,
            perPage: $request->get('per_page', 10),
            relations: ['location'],
            filterLocation: true
        );

        return response()->json($passengers);
    }

    public function createSchedulesForRoutes(StoreRouteScheduleRequest $request): JsonResponse
    {
        $user = auth()->user();
        $validated = $request->validated();
        $schedules = $this->routePlanService->createSchedulesForRoutes($user, $validated);

        return response()->json([
            'status' => 'success',
            'schedules' => $schedules,
        ], 200);
    }
}
