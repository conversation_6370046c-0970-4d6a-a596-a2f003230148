<?php

namespace App\Http\Controllers\Plan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\StoreScheduleRequest;
use App\Http\Requests\Plan\UpdateScheduleRequest;
use App\Models\Plan\Schedule;
use App\Repositories\Plan\ScheduleRepository;
use App\Services\Plan\ScheduleService;
use Illuminate\Http\RedirectResponse;

class ScheduleController extends Controller
{
    public function __construct(
        protected readonly ScheduleRepository $scheduleRepository,
        protected readonly ScheduleService $scheduleService,
    ) {}

    public function store(StoreScheduleRequest $request): RedirectResponse
    {
        $this->authorize('create', Schedule::class);

        $attributes = $request->getAll();
        $attributes['company_id'] = auth()->user()->company_id;
        // todo validate vehicle is available (check other plans)
        $this->scheduleService->create($attributes);
        $this->addToast(__('messages.success.create', ['model' => __('general.models.schedule')]));

        return back();
    }

    public function update(UpdateScheduleRequest $request, Schedule $schedule): RedirectResponse
    {
        $this->authorize('update', $schedule);

        // validate vehicle is available (check other plans)
        $this->scheduleRepository->update($schedule, $request->getAll());
        $this->addToast(__('messages.success.update', ['model' => __('general.models.schedule')]));

        return back();
    }

    public function delete(Schedule $schedule): RedirectResponse
    {
        $this->authorize('delete', $schedule);

        $this->scheduleRepository->delete($schedule);
        $this->addToast(__('messages.success.delete', ['model' => __('general.models.schedule')]));

        return back();
    }
}
