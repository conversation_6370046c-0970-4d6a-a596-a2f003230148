<?php

namespace App\Http\Controllers\Fleet;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Fleet\ListVehiclesRequest;
use App\Http\Requests\Fleet\StoreVehicleRequest;
use App\Http\Requests\Fleet\UpdateVehicleRequest;
use App\Http\Resources\Fleet\VehicleResource;
use App\Models\Fleet\Vehicle;
use App\Services\Fleet\VehicleService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class VehicleController extends Controller
{
    public function __construct(
        private readonly VehicleService $vehicleService
    ) {}

    public function list(ListVehiclesRequest $request): Response
    {
        return inertia('Fleet/Vehicles/List/Main', [
            'vehicles' => $this->vehicleService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true
            ),
        ]);
    }

    public function view(Vehicle $vehicle): Response|RedirectResponse
    {
        return Inertia::render('Fleet/Vehicles/View/Main', [
            'vehicle' => $vehicle,
        ]);
    }

    public function save(StoreVehicleRequest $request): RedirectResponse
    {
        $vehicle = $this->vehicleService->save($request->validated());
        $this->notifyModelUpdate(ModelType::Vehicle, 'create');

        return to_route('fleet.vehicles.view', $vehicle->id);
    }

    public function update(Vehicle $vehicle, UpdateVehicleRequest $request): RedirectResponse
    {
        $this->vehicleService->update($vehicle, $request->validated());
        $this->notifyModelUpdate(ModelType::Vehicle, 'update');

        return to_route('fleet.vehicles.view', $vehicle->id);
    }

    public function delete(Vehicle $vehicle): RedirectResponse
    {
        // if ($this->modelDeletionCheckService->canDelete(Vehicle::class, $vehicle->id)) {
        //     $this->addToast('transport.vehicle.messages.cant_delete', 'general.deletion_error', 'error');

        //     return back();
        // }

        $this->vehicleService->delete($vehicle);
        $this->notifyModelUpdate(ModelType::Vehicle, 'delete');

        return to_route('fleet.vehicles.list');
    }

    public function get(Vehicle $vehicle): VehicleResource
    {
        return VehicleResource::make($vehicle);
    }
}
