<?php

namespace App\Http\Controllers\Fleet;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Fleet\ListDriversRequest;
use App\Http\Requests\Fleet\StoreDriverRequest;
use App\Http\Requests\Fleet\UpdateDriverRequest;
use App\Http\Resources\Fleet\DriverResource;
use App\Models\Fleet\Driver;
use App\Services\Fleet\DriverService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class DriverController extends Controller
{
    public function __construct(
        private readonly DriverService $driverService
    ) {}

    public function list(ListDriversRequest $request): Response
    {
        return inertia('Fleet/Drivers/List/Main', [
            'drivers' => $this->driverService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true
            ),
        ]);
    }

    public function view(Driver $driver): Response|RedirectResponse
    {
        return Inertia::render('Fleet/Drivers/View/Main', [
            'driver' => $driver,
        ]);
    }

    public function viewRides(Driver $driver): Response|RedirectResponse
    {
        return Inertia::render('Fleet/Drivers/View/Rides/Main', [
            'driver' => $driver->loadMissing(['rides.route.origin', 'rides.route.destination', 'rides.vehicle', 'rides.attendances.passenger']),
        ]);
    }

    public function save(StoreDriverRequest $request): RedirectResponse
    {
        $driver = $this->driverService->save($request->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::Driver, 'create');

        return to_route('fleet.drivers.view', $driver->id);
    }

    public function update(Driver $driver, UpdateDriverRequest $request): RedirectResponse
    {
        $this->driverService->update($driver, $request->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::Driver, 'update');

        return to_route('fleet.drivers.view', $driver->id);
    }

    public function delete(Driver $driver): RedirectResponse
    {
        $this->driverService->delete($driver);
        $this->notifyModelUpdate(ModelType::Driver, 'delete');

        return to_route('fleet.drivers.list');
    }

    public function get(Driver $driver): DriverResource
    {
        return DriverResource::make($driver);
    }
}
