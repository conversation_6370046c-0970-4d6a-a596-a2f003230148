<?php

namespace App\Http\Controllers\Fleet;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Fleet\ListAssistantsRequest;
use App\Http\Requests\Fleet\StoreAssistantRequest;
use App\Http\Requests\Fleet\UpdateAssistantRequest;
use App\Models\Fleet\Assistant;
use App\Services\Fleet\AssistantService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class AssistantController extends Controller
{
    public function __construct(
        private readonly AssistantService $assistantService
    ) {}

    public function list(ListAssistantsRequest $request): Response
    {
        return inertia('Fleet/Assistants/List/Main', [
            'assistants' => $this->assistantService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true
            ),
        ]);
    }

    public function view(Assistant $assistant): Response|RedirectResponse
    {
        return Inertia::render('Fleet/Assistants/View/Main', [
            'assistant' => $assistant,
        ]);
    }

    public function save(Assistant $assistant, StoreAssistantRequest $storeAssistantRequest): RedirectResponse
    {
        $assistant = $this->assistantService->save($storeAssistantRequest->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::Assistant, 'create');

        return to_route('fleet.assistants.view', $assistant->id);
    }

    public function update(Assistant $assistant, UpdateAssistantRequest $updateAssistantRequest): RedirectResponse
    {
        $this->assistantService->update($assistant, $updateAssistantRequest->validated());
        $this->notifyModelUpdate(ModelType::Assistant, 'update');

        return to_route('fleet.assistants.view', $assistant->id);
    }

    public function delete(Assistant $assistant): RedirectResponse
    {
        $this->assistantService->delete($assistant);
        $this->notifyModelUpdate(ModelType::Assistant, 'delete');

        return to_route('fleet.assistants.list');
    }
}
