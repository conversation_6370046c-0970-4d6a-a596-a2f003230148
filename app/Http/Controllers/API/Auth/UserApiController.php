<?php

namespace App\Http\Controllers\API\Auth;

use App\Enums\General\Models;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Settings\ConfirmUpdatePhoneApiRequest;
use App\Http\Requests\Api\Settings\UpdateImageApiRequest;
use App\Http\Requests\Api\Settings\UpdateLocaleApiRequest;
use App\Http\Requests\Api\Settings\UpdatePhoneApiRequest;
use App\Http\Resources\Auth\UserResource;
use App\Http\Resources\Fleet\AssistantResource;
use App\Http\Resources\Fleet\DriverResource;
use App\Http\Resources\Transport\PassengerResource;
use App\Http\Resources\Transport\ResponsibleResource;
use App\Models\Auth\User;
use App\Models\Fleet\Assistant;
use App\Models\Fleet\Driver;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Notifications\Auth\VerifyOtpNotification;
use App\Services\User\UserAuthService;
use Illuminate\Http\JsonResponse;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

class UserApiController extends Controller
{
    public function __construct(
        private readonly UserAuthService $userAuthService
    ) {}

    public function view(): ResponsibleResource|DriverResource|UserResource|AssistantResource|PassengerResource
    {
        $user = auth()->user();
        $fields = match ($user->type()) {
            Models::Passenger => ['location', 'company.location'],
            default => ['company.location']
        };
        $user->load($fields);

        return UserResource::getResource($user);
    }

    public function updateImage(UpdateImageApiRequest $request): ResponsibleResource|DriverResource|UserResource|AssistantResource|PassengerResource
    {
        /** @var Driver|Passenger|User|Assistant|Responsible $user */
        $user = $request->getSubjectUser();

        $user->clearMediaCollection('image');
        $user->addMediaFromRequest('image')->toMediaCollection('image');
        $user->refresh();

        return UserResource::getResource($user);
    }

    #[Endpoint('Update phone')]
    #[Group('Settings')]
    #[Subgroup('Phone')]
    public function updatePhone(UpdatePhoneApiRequest $request): DriverResource|UserResource|PassengerResource|AssistantResource|ResponsibleResource
    {
        /** @var Driver|Passenger|User|Assistant|Responsible $user */
        $user = auth()->user();
        $user->setOtp(mt_rand(0000, 9999));
        $user->phone = $request->phone; // temp
        $user->notify(new VerifyOtpNotification);

        return UserResource::getResource($user);
    }

    #[Endpoint('Verify phone update')]
    #[Group('Settings')]
    #[Subgroup('Phone')]
    public function confirmUpdatePhone(ConfirmUpdatePhoneApiRequest $request): UserResource|AssistantResource|DriverResource|PassengerResource|ResponsibleResource|JsonResponse
    {
        /** @var Driver|Passenger|User|Assistant|Responsible $user */
        $user = auth()->user();

        if (! $user->verifyOtp($request->otp)) {
            // todo implement resource
            return response()->json([
                'success' => false,
                'message' => 'cannot update phone',
            ]);
        }
        $this->userAuthService->updatePhone($user, $request->phone);

        return UserResource::getResource($user->refresh());
    }

    #[Endpoint('Update user language')]
    #[Group('Settings')]
    #[Subgroup('Phone')]
    public function updateLocale(UpdateLocaleApiRequest $request): void
    {
        /** @var Driver|Passenger|User|Assistant|Responsible $user */
        $user = auth()->user();

        $this->userAuthService->updateLocale($user, $request->language);
    }
}
