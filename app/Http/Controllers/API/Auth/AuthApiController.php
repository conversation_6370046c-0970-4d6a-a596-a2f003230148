<?php

namespace App\Http\Controllers\API\Auth;

use App\Enums\System\Queue;
use App\Enums\System\UserEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Auth\SendOtpApiRequest;
use App\Http\Requests\Api\Auth\VerifyOtpApiRequest;
use App\Http\Resources\Auth\AuthResource;
use App\Notifications\Auth\VerifyOtpNotification;
use App\Services\User\UserAuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;

class AuthApiController extends ApiController
{
    public function __construct(
        private readonly UserAuthService $userAuthService,
    ) {}

    #[Endpoint('Sent auth OTP')]
    #[Group('Auth')]
    public function sendOtp(SendOtpApiRequest $request): void
    {
        $user = $this->userAuthService->findByPhone($request->phone, $request->package);

        if (! $user) {
            return;
        }

        if (app()->isProduction() and $user->isInternalUser()) {
            $user->setOtp('1234');

            return;
        }

        $user->setOtp(mt_rand(0000, 9999));

        Notification::send($user, (new VerifyOtpNotification)->onQueue(Queue::Default));

        Mixpanel::trackEvent($user, UserEvent::OtpSent, [
            'company' => $user?->company?->name,
        ]);
    }

    #[Endpoint('Verify auth OTP')]
    #[Group('Auth')]
    public function verifyOtp(VerifyOtpApiRequest $request): JsonResponse|AuthResource
    {
        $user = $this->userAuthService->findByPhone($request->phone, $request->package);

        if (! $user or ($user and ! $user->verifyOtp($request->otp))) {
            return $this->sendError('auth.messages.invalid_otp');
        }

        $accessToken = $user->createToken('yosr');

        $user->load('company.location');

        Mixpanel::trackEvent($user, UserEvent::OtpVerify, [
            'company' => $user?->company?->name,
        ]);

        return AuthResource::make([
            'token' => $accessToken->plainTextToken,
            'user' => $user,
        ]);
    }

    #[Endpoint('Logout')]
    #[Group('Auth')]
    public function logout(): JsonResponse
    {
        $user = Auth::user();
        $user->currentAccessToken()->delete();

        Mixpanel::trackEvent(auth()->user(), UserEvent::UserLogout, [
            'company' => auth()->user()?->company?->name,
        ]);

        return $this->sendResponse(
            null,
            'User logged out successfully.'
        );
    }
}
