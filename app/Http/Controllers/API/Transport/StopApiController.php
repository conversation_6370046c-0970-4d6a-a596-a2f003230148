<?php

namespace App\Http\Controllers\API\Transport;

use App\Enums\General\Models;
use App\Enums\Notification\NotificationType;
use App\Enums\System\UserEvent;
use App\Enums\Transport\StopStatus;
use App\Events\Transport\StopSkippedEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Transport\Stop\DelayStopRequest;
use App\Http\Requests\Api\Transport\Stop\DepartStopApiRequest;
use App\Http\Requests\Api\Transport\Stop\ScheduleStopApiRequest;
use App\Http\Requests\Api\Transport\Stop\SkipStopRequest;
use App\Http\Resources\Transport\StopResource;
use App\Jobs\Notification\RideNotificationJob;
use App\Models\Transport\Stop;
use App\Repositories\Transport\RideRepository;
use App\Repositories\Transport\StopRepository;
use App\Services\Plan\AttendanceService;
use App\Services\Transport\StopService;
use Carbon\Carbon;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class StopApiController extends ApiController
{
    public function __construct(
        private readonly StopService $stopService,
        private readonly AttendanceService $attendanceService,
        private readonly RideRepository $rideRepository,
        private readonly StopRepository $stopRepository,
    ) {}

    #[Endpoint('Schedule stop')]
    #[Group('Transport')]
    #[Subgroup('Stop')]
    #[ResponseFromApiResource(StopResource::class, Stop::class)]
    public function schedule(ScheduleStopApiRequest $request): StopResource
    {
        $ride = $this->rideRepository->find($request->ride_id);
        $this->authorize('schedule', [
            Stop::class,
            $ride,
        ]);

        $stop = $this->stopRepository->updateOrCreate([
            'ride_id' => $request->ride_id,
            'station_id' => $request->station_id,
            'company_id' => auth()->user()->company_id,
            'status' => StopStatus::Scheduled,
        ], [
            'scheduled_at' => $request->scheduled_at,
        ]);

        $passengers = $this->attendanceService->getExpectedPassengersForStop($stop);

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::StopScheduled,
            [
                'ride_id' => $ride->id,
                'time' => $stop->scheduled_at->format('H:i'),
            ]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::StopScheduled, [
            'company' => auth()->user()?->company?->name,
            'ride_id' => $ride->id,
            'stop_id' => $stop->id,
        ]);

        return StopResource::make($stop);
    }

    #[Endpoint('Arrive stop')]
    #[Group('Transport')]
    #[Subgroup('Stop')]
    #[ResponseFromApiResource(StopResource::class, Stop::class)]
    public function arrive(Stop $stop): StopResource
    {
        $this->authorize('arrive', $stop);

        $stop = $this->stopService->arrive($stop);
        $passengers = $this->attendanceService->getExpectedPassengersForStop($stop);

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::StopArrived,
            [
                'ride_id' => $stop->ride_id,
                'stop_id' => $stop->id,
            ]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::StopArrived, [
            'company' => auth()->user()?->company?->name,
            'ride_id' => $stop->ride_id,
            'stop_id' => $stop->id,
        ]);

        return StopResource::make($stop);
    }

    #[Endpoint('Depart stop')]
    #[Group('Transport')]
    #[Subgroup('Stop')]
    #[ResponseFromApiResource(StopResource::class, Stop::class)]
    public function depart(DepartStopApiRequest $request, Stop $stop): StopResource
    {
        $this->authorize('depart', $stop);

        $stop = $this->stopService->depart($stop);
        $passengers = $this->attendanceService->getExpectedPassengersForStop($stop);

        $this->attendanceService->saveStopAttendances(
            $stop,
            $passengers->pluck('id')->toArray(),
            today(),
            $request->attendants ?? []
        );

        $noShowPassengers = $this->attendanceService->getNoShowPassengers($passengers, $request->attendants ?? []);
        $presentPassengers = $this->attendanceService->getPresentPassengers($passengers, $request->attendants ?? []);

        RideNotificationJob::dispatch(
            $noShowPassengers,
            NotificationType::StopNoShow,
            ['ride_id' => $stop->ride_id]
        );

        RideNotificationJob::dispatch(
            $presentPassengers,
            NotificationType::StopDeparted,
            ['ride_id' => $stop->ride_id]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::StopDeparted, [
            'company' => auth()->user()?->company?->name,
            'ride_id' => $stop->ride_id,
            'stop_id' => $stop->id,
        ]);

        return StopResource::make($stop);
    }

    #[Endpoint('Delay stop')]
    #[Group('Transport')]
    #[Subgroup('Stop')]
    #[ResponseFromApiResource(StopResource::class, Stop::class)]
    public function delay(DelayStopRequest $request, Stop $stop): StopResource
    {
        $this->authorize('delay', $stop);

        $stop = $this->stopService->depart($stop);
        $passengers = $this->attendanceService->getExpectedPassengersForStop($stop);
        $newScheduledTime = Carbon::make($request->new_scheduled_time);

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::StopDelayed,
            [
                'ride_id' => $stop->ride_id,
                'stop_id' => $stop->id,
                'scheduled_at' => $newScheduledTime,
            ]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::StopDelayed, [
            'company' => auth()->user()?->company?->name,
            'ride_id' => $stop->ride_id,
            'stop_id' => $stop->id,
        ]);

        return StopResource::make($stop);
    }

    #[Endpoint('Skip station')]
    #[Group('Transport')]
    #[Subgroup('Stop')]
    #[ResponseFromApiResource(StopResource::class, Stop::class)]
    public function skip(SkipStopRequest $request): StopResource
    {
        $ride = $this->rideRepository->findOrFail($request->ride_id);
        $this->authorize('hasAccess', $ride);

        $stop = $this->stopRepository->updateOrCreate([
            'ride_id' => $request->ride_id,
            'station_id' => $request->station_id,
            'company_id' => auth()->user()->company_id,
        ], [
            'status' => StopStatus::Skipped,
        ]);

        broadcast(new StopSkippedEvent([
            'ride_id' => $ride->id,
            'stop_id' => $stop->id,
        ]));

        $passengers = $this->attendanceService->getExpectedPassengersForStop($stop);

        $passengers->each(function ($passenger) use ($ride) {
            $this->attendanceService->updateOrCreate([
                'passenger_id' => $passenger->id,
                'schedule_id' => $ride->schedule_id,
                'company_id' => $ride->company_id,
                'date' => today(),
            ], [
                'status' => StopStatus::Skipped,
                'ride_id' => $ride->id,
                'creator_type' => Models::Driver,
                'creator_id' => $ride->driver_id,
            ]);
        });

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::StopSkipped,
            ['ride_id' => $ride->id, 'reason' => $request->reason]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::StopSkipped, [
            'company' => auth()->user()?->company?->name,
            'ride_id' => $stop->ride_id,
            'stop_id' => $stop->id,
        ]);

        return StopResource::make($stop);
    }
}
