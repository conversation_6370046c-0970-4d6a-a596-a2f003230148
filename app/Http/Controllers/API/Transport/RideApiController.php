<?php

namespace App\Http\Controllers\API\Transport;

use App\Enums\General\Models;
use App\Enums\Notification\NotificationType;
use App\Enums\System\UserEvent;
use App\Events\Transport\RideArrivedEvent;
use App\Events\Transport\RideCanceledEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Transport\Ride\ArriveRideApiRequest;
use App\Http\Requests\Api\Transport\Ride\CancelRideApiRequest;
use App\Http\Requests\Api\Transport\Ride\GetOnGoingRideApiRequest;
use App\Http\Requests\Api\Transport\Ride\ListRidesApiRequest;
use App\Http\Requests\Api\Transport\Ride\RequestRideRequest;
use App\Http\Requests\Api\Transport\Ride\StartRideApiRequest;
use App\Http\Resources\Transport\RideItemResource;
use App\Http\Resources\Transport\RideResource;
use App\Jobs\Notification\RideNotificationJob;
use App\Models\Fleet\Driver;
use App\Models\Transport\Ride;
use App\Repositories\Plan\ScheduleRepository;
use App\Services\Plan\AttendanceService;
use App\Services\Transport\RideService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class RideApiController extends ApiController
{
    public function __construct(
        private readonly RideService $rideService,
        private readonly ScheduleRepository $scheduleRepository,
        private readonly AttendanceService $attendanceService,
    ) {}

    #[Endpoint('List rides')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class, collection: true)]
    public function list(ListRidesApiRequest $request): AnonymousResourceCollection
    {
        $user = $request->getSubjectUser();
        $this->authorize('view', $user);

        $rides = $this->rideService->getItems($user, [
            'day' => $request->day,
        ]);

        return RideItemResource::collection($rides);
    }

    #[Endpoint('Start/Resume ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function start(StartRideApiRequest $request): RideResource
    {
        $schedule = $this->scheduleRepository->find($request->schedule_id);
        $this->authorize('start', [
            Ride::class,
            $schedule,
        ]);

        /** @var Driver $driver */
        $driver = auth()->user();

        $ride = $this->rideService->getOngoingRideForSchedule($schedule, now())
            ?? $this->rideService->start($schedule, $driver, $request->validated());

        $passengers = $this->rideService->getExpectedPassengers($ride);

        $ignoredPassengers = $this->attendanceService->markIgnoredPassengersByStations(
            $ride,
            $passengers,
            $request->stations,
            $this->rideService->getFinishedStationIds($ride)
        );

        RideNotificationJob::dispatch(
            $ignoredPassengers,
            NotificationType::StopIgnored,
            ['ride_id' => $ride->id]
        );

        $remainingPassengers = $passengers->diff($ignoredPassengers);

        RideNotificationJob::dispatch(
            $remainingPassengers,
            NotificationType::RideStarted,
            ['ride_id' => $ride->id]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::RideStarted, [
            'ride_id' => $ride->id,
            'company' => $driver->company->name,
            'route' => $ride->route->name,
        ]);

        return RideResource::make($ride);
    }

    #[Endpoint('Cancel ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function cancel(CancelRideApiRequest $request, Ride $ride): RideResource
    {
        $this->authorize('cancel', $ride);

        $ride = $this->rideService->cancel($ride, $request->validated());

        broadcast(new RideCanceledEvent([
            'ride_id' => $ride->id,
        ]));

        $passengers = $this->rideService->getExpectedPassengers($ride);

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::RideCanceled,
            ['ride_id' => $ride->id]
        );

        $ride->unsetRelation('route');

        Mixpanel::trackEvent(auth()->user(), UserEvent::RideCanceled, [
            'ride_id' => $ride->id,
            'company' => auth()->user()?->company?->name,
            'route' => $ride?->route->name,
        ]);

        return RideResource::make($ride);
    }

    #[Endpoint('Arrive ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function arrive(ArriveRideApiRequest $request, Ride $ride): RideResource
    {
        $this->authorize('arrive', $ride);

        $ride = $this->rideService->arrive($ride, $request->validated());

        broadcast(new RideArrivedEvent([
            'ride_id' => $ride->id,
        ]));

        $passengers = $this->rideService->getPresentPassengers($ride);

        RideNotificationJob::dispatch(
            $passengers,
            NotificationType::RideArrived,
            ['ride_id' => $ride->id],
            [Models::Passenger]
        );

        Mixpanel::trackEvent(auth()->user(), UserEvent::RideArrived, [
            'ride_id' => $ride->id,
            'company' => auth()->user()?->company?->name,
            'route' => $ride?->route->name,
        ]);

        return RideResource::make($ride);
    }

    #[Endpoint('Get ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function get(Ride $ride): RideResource
    {
        $ride->load([
            'route',
            'route.stations.passengers',
            'route.stations.location',
        ]);

        return RideResource::make($ride);
    }

    #[Endpoint('Get ongoing ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function ongoing(GetOnGoingRideApiRequest $request): Response|RideResource
    {
        $ride = $this->rideService->getOngoingForUser($request->getSubjectUser());

        Mixpanel::trackEvent(auth()->user(), UserEvent::ViewLiveMap, [
            'company' => auth()->user()?->company?->name,
        ]);

        return $ride ? RideResource::make($ride) : response()->noContent();
    }

    #[Endpoint('Request ride for passenger')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    #[ResponseFromApiResource(RideResource::class, Ride::class)]
    public function request(RequestRideRequest $request): void
    {
        Mixpanel::trackEvent(auth()->user(), UserEvent::RideRequested, [
            'company' => auth()->user()?->company?->name,
            'passenger_id' => $request->passenger_id,
        ]);

        // todo implement logic for request ride for passenger
    }
}
