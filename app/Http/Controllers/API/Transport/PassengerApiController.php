<?php

namespace App\Http\Controllers\API\Transport;

use App\Http\Controllers\API\ApiController;
use App\Http\Resources\Transport\PassengerResource;
use App\Models\Transport\Passenger;
use App\Services\Transport\PassengerService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use <PERSON>nuckle<PERSON>\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class PassengerApiController extends ApiController
{
    public function __construct(
        private readonly PassengerService $passengerService,
    ) {}

    #[Endpoint('List passengers')]
    #[Group('Transport')]
    #[Subgroup('Passenger')]
    #[ResponseFromApiResource(PassengerResource::class, Passenger::class, collection: true)]
    public function list(): AnonymousResourceCollection
    {
        $this->authorize('list', Passenger::class);

        return PassengerResource::collection(
            $this->passengerService->listByResponsible(auth()->user())->unique()
        );
    }

    #[Endpoint('Get passenger')]
    #[Group('Transport')]
    #[Subgroup('Passenger')]
    #[ResponseFromApiResource(PassengerResource::class, Passenger::class, collection: true)]
    public function get(Passenger $passenger): PassengerResource
    {
        $this->authorize('view', $passenger);

        $passenger->load([
            'locations',
        ]);

        return PassengerResource::make($passenger);
    }
}
