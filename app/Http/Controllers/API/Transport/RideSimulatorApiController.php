<?php

namespace App\Http\Controllers\API\Transport;

use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Transport\Ride\SimulateRideApiRequest;
use App\Jobs\Simulation\SimulateRideJob;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;

class RideSimulatorApiController extends ApiController
{
    #[Endpoint('Simulate ride')]
    #[Group('Transport')]
    #[Subgroup('Ride')]
    public function simulate(SimulateRideApiRequest $request): void
    {
        if (app()->environment('production')) {
            abort(404);
        }

        SimulateRideJob::dispatch(
            scheduleId: $request->schedule_id,
            speed: $request->speed ?? 'normal',
            allowAbsence: $request->allow_absence ?? true,
            allowDriverStopSkip: $request->allow_driver_stop_skip ?? false,
            allowPassengerStopCancel: $request->allow_passenger_stop_cancel ?? false,
            allowDriverCancelRide: $request->allow_driver_cancel_ride ?? false,
        );
    }
}
