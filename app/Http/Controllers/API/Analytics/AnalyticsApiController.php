<?php

namespace App\Http\Controllers\API\Analytics;

use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Analytics\GetReportRequest;
use App\Http\Resources\Analytics\ReportResource;
use App\Services\Analytics\AnalyticsService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;

class AnalyticsApiController extends ApiController
{
    private AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    #[Endpoint('Analytics Reports')]
    #[Group('Analytics')]
    public function get(GetReportRequest $request): AnonymousResourceCollection
    {
        $reportConfigs = [
            'total-number-of-rides' => ['type' => 'state', 'icon' => 'fa-road', 'withChart' => false],
            'ride-duration-metrics' => ['type' => 'bar', 'withChart' => true],
            'ride-distance-metrics' => ['type' => 'bar', 'withChart' => true],
            'ride-status-counts' => ['type' => 'state', 'icon' => 'fa-road', 'withChart' => true],
            'total-number-of-passengers' => ['type' => 'state', 'icon' => 'fa-person-seat', 'withChart' => false],
            'passenger-demographics' => ['type' => 'state', 'icon' => 'fa-users', 'withChart' => true],
            'total-distance-traveled' => ['type' => 'state', 'icon' => 'fa-infinity', 'withChart' => false],
            'average-driver-speed' => ['type' => 'bar', 'withChart' => true],
            'average-vehicle-usage' => ['type' => 'bar', 'withChart' => true],
            'ride-counts-by-driver' => ['type' => 'pie', 'withChart' => true],
            'ride-counts-by-vehicle' => ['type' => 'doughnut', 'withChart' => true],
            'total-number-of-vehicles' => ['type' => 'state', 'icon' => 'fa-car', 'withChart' => false],
            'driver-demographics' => ['type' => 'state', 'icon' => 'fa-user-tie', 'withChart' => true],
        ];

        $reports = [];
        foreach ($reportConfigs as $key => $config) {
            $data = $this->analyticsService->getAnalytics($key, $request->filters ?? []);
            $reports[$key] = array_merge(['key' => $key, 'data' => $data], $config);
        }

        return ReportResource::collection($reports);
    }
}
