<?php

namespace App\Http\Controllers\API\Geo;

use App\Http\Controllers\API\ApiController;
use App\Http\Resources\Fleet\DriverResource;
use App\Http\Resources\Transport\RideResource;
use App\Models\Fleet\Driver;
use App\Services\Fleet\DriverService;
use App\Services\Transport\RideService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

class LiveLocationApiController extends ApiController
{
    public function __construct(
        private readonly DriverService $driverService,
        private readonly RideService $rideService,
    ) {}

    #[Endpoint('List drivers for live map')]
    #[Group('Geo')]
    #[SubGroup('LiveLocation')]
    public function drivers(): AnonymousResourceCollection
    {
        $drivers = $this->driverService->listForMobileMap(auth()->user()->company);

        return DriverResource::collection($drivers);
    }

    #[Endpoint('Get driver ride for live map')]
    #[Group('Geo')]
    #[SubGroup('LiveLocation')]
    public function driversRide(Driver $driver): RideResource
    {
        $this->authorize('view', $driver);

        $ride = $this->rideService->getForMobileMap($driver);

        return RideResource::make($ride);
    }
}
