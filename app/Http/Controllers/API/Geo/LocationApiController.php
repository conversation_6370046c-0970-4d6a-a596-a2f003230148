<?php

namespace App\Http\Controllers\API\Geo;

use App\Enums\System\UserEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Geo\ListLocationApiRequest;
use App\Http\Requests\Api\Geo\StoreLocationApiRequest;
use App\Http\Requests\Api\Geo\UpdateLocationApiRequest;
use App\Http\Resources\Geo\LocationResource;
use App\Models\Geo\Location;
use App\Services\Geo\LocationService;
use App\Services\Transport\PassengerService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class LocationApiController extends ApiController
{
    public function __construct(
        private readonly LocationService $locationService,
        private readonly PassengerService $passengerService,
    ) {}

    #[Endpoint('Store location')]
    #[Group('Geo')]
    #[SubGroup('Location')]
    #[ResponseFromApiResource(LocationResource::class, Location::class)]
    public function store(StoreLocationApiRequest $request): LocationResource
    {
        $passenger = $this->passengerService->find($request->model_id);
        $this->authorize('update', $passenger);

        $attributes = $request->validated();
        $attributes['creator_type'] = $request->user()->type();
        $attributes['creator_id'] = auth()->id();
        $attributes['company_id'] = auth()->user()->company_id;

        $location = $this->locationService->save($attributes);

        Mixpanel::trackEvent(auth()->user(), UserEvent::LocationCreated, [
            'location_id' => $location->id,
            'company' => auth()->user()?->company?->name,
        ]);

        return LocationResource::make($location);
    }

    #[Endpoint('Update location')]
    #[Group('Geo')]
    #[SubGroup('Location')]
    #[ResponseFromApiResource(LocationResource::class, Location::class)]
    public function update(UpdateLocationApiRequest $request, Location $location): LocationResource
    {
        $this->authorize('update', $location->model);

        $location = $this->locationService->update($location, $request->validated());

        Mixpanel::trackEvent(auth()->user(), UserEvent::LocationUpdated, [
            'location_id' => $location->id,
            'company' => auth()->user()?->company?->name,
        ]);

        return LocationResource::make($location);
    }

    #[Endpoint('Delete location')]
    #[Group('Geo')]
    #[SubGroup('Location')]
    public function delete(Location $location): void
    {
        $this->authorize('delete', $location);

        Mixpanel::trackEvent(auth()->user(), UserEvent::LocationDeleted, [
            'location_id' => $location->id,
            'company' => auth()->user()?->company?->name,
        ]);

        $this->locationService->delete($location);
    }

    #[Endpoint('List locations')]
    #[Group('Geo')]
    #[SubGroup('Location')]
    #[ResponseFromApiResource(LocationResource::class, Location::class, collection: true)]
    public function list(ListLocationApiRequest $request): AnonymousResourceCollection
    {
        $passenger = $this->passengerService->find($request->passenger_id);
        $this->authorize('view', $passenger);

        return LocationResource::collection($passenger->locations);
    }
}
