<?php

namespace App\Http\Controllers\API\Plan;

use App\Enums\Plan\AttendanceStatus;
use App\Enums\System\UserEvent;
use App\Events\Transport\AttendanceCanceledEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Plan\ListAbsenceApiRequest;
use App\Http\Requests\Api\Plan\StoreAbsenceApiRequest;
use App\Http\Resources\Plan\AbsenceResource;
use App\Models\Plan\Absence;
use App\Repositories\Plan\AttendanceRepository;
use App\Services\Plan\AbsenceService;
use App\Services\Transport\PassengerService;
use App\Services\Transport\RideService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class AbsenceApiController extends ApiController
{
    public function __construct(
        private readonly AbsenceService $absenceService,
        private readonly PassengerService $passengerService,
        private readonly RideService $rideService,
        private readonly AttendanceRepository $attendanceRepository,
    ) {}

    #[Endpoint('Store absence')]
    #[Group('Plan')]
    #[SubGroup('Absence')]
    #[ResponseFromApiResource(AbsenceResource::class, Absence::class)]
    public function store(StoreAbsenceApiRequest $request)
    {
        $passenger = $this->passengerService->find($request->passenger_id);
        $this->authorize('update', $passenger);

        $absence = $this->absenceService->store([
            'for' => $request->for,
            'from' => Carbon::make($request->from),
            'to' => Carbon::make($request->to),
            'reason' => $request->reason,
            'note' => $request->note,
            'passenger_id' => $request->passenger_id,
            'company_id' => auth()->user()->company_id,
            'creator_id' => auth()->user()->id,
            'creator_type' => auth()->user()->type(),
        ]);

        if ($ride = $this->rideService->getOngoingRideForAbsence($absence)) {
            $this->attendanceRepository->updateOrCreate([
                'ride_id' => $ride->id,
                'schedule_id' => $ride->schedule_id,
                'date' => $ride->started_at,
                'passenger_id' => $absence->passenger_id,
                'company_id' => auth()->user()->company_id,
            ], [
                'creator_type' => auth()->user()->type(),
                'creator_id' => auth()->user()->id,
                'status' => AttendanceStatus::Canceled,
            ]);
            broadcast(new AttendanceCanceledEvent([
                'passenger_id' => $request->passenger_id,
                'ride_id' => $ride->id,
            ]));
        }

        Mixpanel::trackEvent(auth()->user(), UserEvent::AbsenceCreated, [
            'absence_id' => $absence->id,
            'company' => auth()->user()?->company?->name,
        ]);

        return response()->noContent(Response::HTTP_CREATED);
    }

    #[Endpoint('Delete absence')]
    #[Group('Plan')]
    #[SubGroup('Absence')]
    public function delete(Absence $absence): void
    {
        $this->authorize('delete', $absence);

        $this->absenceService->delete($absence);

        Mixpanel::trackEvent(auth()->user(), UserEvent::AbsenceDeleted, [
            'absence_id' => $absence->id,
            'company' => auth()->user()?->company?->name,
        ]);
    }

    #[Endpoint('List absences')]
    #[Group('Plan')]
    #[SubGroup('Absence')]
    #[ResponseFromApiResource(AbsenceResource::class, Absence::class, collection: true)]
    public function list(ListAbsenceApiRequest $request): AnonymousResourceCollection
    {
        $passenger = $this->passengerService->find($request->passenger_id);
        $this->authorize('view', $passenger);

        $absences = $this->absenceService->get($passenger, [
            'period' => $request->period,
        ]);

        return AbsenceResource::collection($absences);
    }
}
