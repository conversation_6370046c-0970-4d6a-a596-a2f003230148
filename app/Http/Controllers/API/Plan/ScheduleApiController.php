<?php

namespace App\Http\Controllers\API\Plan;

use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Plan\GetScheduleApiRequest;
use App\Http\Requests\Api\Plan\ListSchedulesApiRequest;
use App\Http\Resources\Plan\ScheduleResource;
use App\Models\Plan\Schedule;
use App\Repositories\Plan\ScheduleRepository;
use App\Services\Plan\AttendanceService;
use App\Services\Plan\ScheduleService;
use App\Services\Transport\RideService;
use App\Services\Transport\RouteOptimizationService;
use App\Services\Transport\RouteService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class ScheduleApiController extends ApiController
{
    public function __construct(
        protected readonly ScheduleService $scheduleService,
        protected readonly AttendanceService $attendanceService,
        protected readonly ScheduleRepository $scheduleRepository,
        protected readonly RouteService $routeService,
        protected readonly RouteOptimizationService $routeOptimizationService,
        protected readonly RideService $rideService,
    ) {}

    #[Endpoint('List schedules')]
    #[Group('Plan')]
    #[SubGroup('Schedule')]
    #[ResponseFromApiResource(ScheduleResource::class, Schedule::class, collection: true)]
    public function list(ListSchedulesApiRequest $request): AnonymousResourceCollection
    {
        $user = $request->getSubjectUser();
        $this->authorize('list', [
            Schedule::class,
            $user,
        ]);

        $schedules = $this->scheduleService->list(
            $user,
            Carbon::make($request->day)
        );

        return ScheduleResource::collection($schedules);
    }

    #[Endpoint('Get schedule')]
    #[Group('Plan')]
    #[SubGroup('Schedule')]
    #[ResponseFromApiResource(ScheduleResource::class, Schedule::class)]
    public function get(Schedule $schedule, GetScheduleApiRequest $request): ScheduleResource
    {
        $user = $request->getSubjectUser();
        $this->authorize('view', $schedule);
        $date = Carbon::make($request->day);

        if ($user->isDriver()) {
            $this->routeOptimizationService->optimizeStationsSequence($schedule->route);
        }

        $schedule = $this->scheduleService->get(
            $schedule,
            $user,
            $date
        );

        if (! $user->isPassenger()) {
            $this->attendanceService->unsetAbsentPassengers($schedule, $date);
        }

        if ($user->isPassenger()) {
            $this->scheduleService->prepareForPassenger($schedule, $user, $date);
        }

        return ScheduleResource::make($schedule);
    }
}
