<?php

namespace App\Http\Controllers\API\Plan;

use App\Enums\Plan\AttendanceStatus;
use App\Enums\System\UserEvent;
use App\Events\Transport\AttendanceCanceledEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Plan\ListAttendanceApiRequest;
use App\Http\Requests\Api\Plan\StoreAttendanceApiRequest;
use App\Http\Resources\Plan\AttendanceItemResource;
use App\Http\Resources\Plan\AttendanceResource;
use App\Models\Plan\Attendance;
use App\Repositories\Plan\AttendanceRepository;
use App\Services\Plan\AttendanceService;
use App\Services\Transport\PassengerService;
use App\Services\Transport\RideService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Knuckles\Scribe\Attributes\Subgroup;

class AttendanceApiController extends ApiController
{
    public function __construct(
        private readonly AttendanceService $attendanceService,
        private readonly AttendanceRepository $attendanceRepository,
        private readonly PassengerService $passengerService,
        private readonly RideService $rideService,
    ) {}

    #[Endpoint('Store attendance')]
    #[Group('Plan')]
    #[SubGroup('Attendance')]
    #[ResponseFromApiResource(AttendanceResource::class, Attendance::class)]
    public function store(StoreAttendanceApiRequest $request)
    {
        $passenger = $this->passengerService->find($request->passenger_id);
        $this->authorize('update', $passenger);

        $attendance = $this->attendanceService->updateOrCreate([
            'schedule_id' => $request->schedule_id,
            'date' => Carbon::make($request->date),
            'company_id' => auth()->user()->company_id,
            'passenger_id' => $request->passenger_id,
        ], [
            'ride_id' => $request->ride_id,
            'status' => $request->status,
            'creator_type' => auth()->user()->id,
            'creator_id' => auth()->user()->id,
        ]);

        $rideId = $request->ride_id ?? null;

        if ($ride = $this->rideService->getOngoingRideForAttendance($attendance)) {
            $this->attendanceRepository->update($attendance, [
                'status', AttendanceStatus::Canceled,
            ]);
            $rideId = $ride->id;
        }

        if ($rideId) {
            broadcast(new AttendanceCanceledEvent([
                'passenger_id' => $request->passenger_id,
                'ride_id' => $rideId,
            ]));
        }

        Mixpanel::trackEvent(auth()->user(), UserEvent::AttendanceCreated, [
            'attendance_id' => $attendance->id,
            'company' => auth()->user()?->company?->name,
        ]);

        return response()->noContent(Response::HTTP_CREATED);
    }

    #[Endpoint('Delete attendance')]
    #[Group('Plan')]
    #[SubGroup('Attendance')]
    public function delete(Attendance $attendance): void
    {
        $this->authorize('delete', $attendance);

        $this->attendanceService->delete($attendance);

        Mixpanel::trackEvent(auth()->user(), UserEvent::AttendanceDeleted, [
            'attendance_id' => $attendance->id,
            'company' => auth()->user()?->company?->name,
        ]);
    }

    #[Endpoint('List attendances')]
    #[Group('Plan')]
    #[SubGroup('Attendance')]
    #[ResponseFromApiResource(AttendanceResource::class, Attendance::class, collection: true)]
    public function list(ListAttendanceApiRequest $request): AnonymousResourceCollection
    {
        $passenger = $this->passengerService->find($request->passenger_id);
        $this->authorize('view', $passenger);

        $attendances = $this->attendanceService->getItems($passenger, [
            'period' => $request->period,
        ]);

        return AttendanceItemResource::collection($attendances);
    }
}
