<?php

namespace App\Http\Controllers\API\Fleet;

use App\Http\Controllers\API\ApiController;
use App\Http\Resources\Fleet\DriverResource;
use App\Models\Fleet\Driver;
use App\Services\Fleet\DriverService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;

class DriverApiController extends ApiController
{
    public function __construct(
        private readonly DriverService $driverService
    ) {}

    #[Endpoint('List drivers')]
    #[Group('Fleet')]
    #[ResponseFromApiResource(DriverResource::class, Driver::class, collection: true)]
    public function list(): AnonymousResourceCollection
    {
        $drivers = $this->driverService->list(auth()->user()->company);

        return DriverResource::collection($drivers);
    }
}
