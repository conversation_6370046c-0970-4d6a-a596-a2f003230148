<?php

namespace App\Http\Controllers\API\Review;

use App\Enums\System\UserEvent;
use App\Facades\Support\Mixpanel;
use App\Http\Controllers\API\ApiController;
use App\Http\Requests\Api\Review\StoreReviewApiRequest;
use App\Http\Resources\Review\ReviewResource;
use App\Models\Review\Review;
use App\Services\Review\ReviewService;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;

class ReviewApiController extends ApiController
{
    public function __construct(
        private readonly ReviewService $reviewService,
    ) {}

    #[Endpoint('Store review')]
    #[Group('Review')]
    #[ResponseFromApiResource(ReviewResource::class, Review::class)]
    public function store(StoreReviewApiRequest $request): ReviewResource
    {
        $this->authorize('create', Review::class);

        $user = auth()->user();
        $review = $this->reviewService->save([
            'reviewer_type' => $user->type(),
            'reviewer_id' => $user->id,
            'company_id' => $user->company_id,
            ...$request->validated(),
        ]);

        Mixpanel::trackEvent(auth()->user(), UserEvent::ReviewCreated, [
            'review_id' => $review->id,
            'company' => auth()->user()?->company?->name,
        ]);

        return ReviewResource::make($review);
    }
}
