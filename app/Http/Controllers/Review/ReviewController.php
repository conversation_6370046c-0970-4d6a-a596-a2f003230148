<?php

namespace App\Http\Controllers\Review;

use App\Http\Controllers\Controller;
use App\Http\Requests\Review\ListReviewsRequest;
use App\Models\Review\Review;
use App\Services\Review\ReviewService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;

class ReviewController extends Controller
{
    public function __construct(
        private readonly ReviewService $reviewService
    ) {}

    public function list(ListReviewsRequest $request): Response|RedirectResponse
    {
        $this->authorize('list', Review::class);

        return inertia('Reviews/List/Main', [
            'reviews' => $this->reviewService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true,
                relations: ['reviewer', 'ride.driver']
            ),
        ]);
    }
}
