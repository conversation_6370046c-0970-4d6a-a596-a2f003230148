<?php

namespace App\Http\Controllers\Setting;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use Inertia\Response;

class SettingController extends Controller
{
    public function list(): Response
    {
        return inertia('Settings/Main', [
            'settings' => config('settings.tabs'),
        ]);
    }

    public function personalSettings(): Response
    {
        return Inertia::render('Settings/Personal/Main', [
            'user' => auth()->user(),
        ]);
    }
}
