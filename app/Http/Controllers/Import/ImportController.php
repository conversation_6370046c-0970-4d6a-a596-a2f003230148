<?php

namespace App\Http\Controllers\Import;

use App\Enums\Import\ImportType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Import\ConfirmImportRequest;
use App\Http\Requests\Import\UploadImportRequest;
use App\Models\Auth\User;
use App\Services\Import\ImportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ImportController extends Controller
{
    public function __construct(
        private readonly ImportService $importService
    ) {}

    public function index(): Response
    {
        return Inertia::render('Imports/Main');
    }

    public function upload(UploadImportRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = User::findOrFail(auth()->user()->id);

        $temporaryMedia = [];
        foreach ($data['temporary_media_ids'] as $temporaryMediaId) {
            $media = $user->getMedia('temp')->firstWhere('id', $temporaryMediaId);
            if ($media) {
                $temporaryMedia[] = $media;
            }
        }

        $fields = match ($data['import_type']) {
            ImportType::Passengers => collect(config('import.attributes.passengers')),
            ImportType::Responsibles => collect(config('import.attributes.responsibles')),
            default => collect()
        };

        $matches = $this->importService->getColumnsFromMedia($temporaryMedia, $fields);

        return response()->json([
            'matches' => $matches,
            'fields' => $fields,
        ]);
    }

    public function confirm(ConfirmImportRequest $request): JsonResponse|RedirectResponse
    {
        $data = $request->validated();
        $user = User::findOrFail(auth()->user()->id);

        $temporaryMedia = [];
        foreach ($data['temporary_media_ids'] as $temporaryMediaId) {
            $media = $user->getMedia('temp')->firstWhere('id', $temporaryMediaId);
            if ($media) {
                $temporaryMedia[] = $media;
            }
        }

        $failures = $this->importService->processImport($data['import_type'], $temporaryMedia[0]->getPath(), $data['matches'], $user);

        $user->clearMediaCollection('temp');

        if (! empty($failures)) {
            return response()->json(['failures' => $failures], 422);
        }

        $this->addToast('messages.custom.import_started_successfully');

        return to_route('home');
    }
}
