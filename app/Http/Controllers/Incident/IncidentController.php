<?php

namespace App\Http\Controllers\Incident;

use App\Http\Controllers\Controller;
use App\Http\Requests\Incident\ListIncidentsRequest;
use App\Services\Incident\IncidentService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;

class IncidentController extends Controller
{
    public function __construct(
        private readonly IncidentService $incidentService
    ) {}

    public function list(ListIncidentsRequest $request): Response|RedirectResponse
    {
        return inertia('Incidents/List/Main', [
            'incidents' => $this->incidentService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true,
                relations: ['reporter', 'ride.driver']
            ),
        ]);
    }
}
