<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\UpdateCompanyRequest;
use App\Http\Resources\General\CompanyResource;
use App\Models\Company\Company;
use App\Services\Company\CompanyService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

class CompanyController extends Controller
{
    public function __construct(
        private readonly CompanyService $companyService,
    ) {}

    public function list(): AnonymousResourceCollection
    {
        $companyId = auth()->user()->getAttributes()['company_id'];

        $company = $this->companyService->find($companyId);
        $companies = $company->subCompanies;
        $companies = collect([$company])->concat($companies);

        return CompanyResource::collection($companies);
    }

    public function view(): Response
    {
        $company = Company::findOrFail(auth()->user()->company_id);

        return Inertia::render('Settings/Company/Company/Main', [
            'company' => $company,
        ]);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function update(UpdateCompanyRequest $request): RedirectResponse
    {
        $company = Company::findOrFail(auth()->user()->company_id);
        $this->companyService->update($company, $request->validated());

        $this->addToast('messages.custom.company_info_saved');

        return back();
    }

    public function select(Company $company): RedirectResponse
    {
        $this->authorize('select', $company);

        session()->put('company_id', $company->id);

        return redirect()->route('home');
    }
}
