<?php

namespace App\Http\Controllers\Transport;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\StoreGroupRequest;
use App\Http\Requests\Transport\UpdateGroupRequest;
use App\Models\Company\Company;
use App\Models\Transport\Group;
use App\Services\General\ModelDeletionCheckService;
use App\Services\Transport\GroupService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class GroupController extends Controller
{
    public function __construct(
        private readonly GroupService $groupService,
        private readonly ModelDeletionCheckService $modelDeletionCheckService
    ) {}

    public function view(?Group $group = null): Response|RedirectResponse
    {
        if ($group) {
            $this->authorize('view', $group);
        }

        if (! $group) {
            if ($group = Group::query()->company(auth()->user()->company_id)->first()) {
                return redirect()->route(request()->route()->getName(), $group->id);
            }
        }

        return Inertia::render('Settings/Transport/Groups/View', [
            'groups' => Group::query()->company(auth()->user()->company_id)->orderBy('id')->get(),
            'group' => $group,
        ]);
    }

    public function save(StoreGroupRequest $request): RedirectResponse
    {
        $this->authorize('create', Group::class);

        $company = Company::findOrFail(auth()->user()->company_id);

        $group = $this->groupService->save($company, $request->validated());

        $this->notifyModelUpdate(ModelType::Group);

        return to_route('settings.transport.groups.view', $group->id);
    }

    public function edit(Group $group): Response
    {
        $this->authorize('update', $group);

        return Inertia::render('Settings/Transport/Groups/Edit', [
            'groups' => Group::query()->company(auth()->user()->company_id)->get(),
            'group' => $group,
        ]);
    }

    public function update(UpdateGroupRequest $request, Group $group): RedirectResponse
    {
        $this->authorize('update', $group);

        $company = Company::findOrFail(auth()->user()->company_id);

        $this->groupService->update($company, $group, $request->validated());

        $this->notifyModelUpdate(ModelType::Group, 'update');

        return to_route('settings.transport.groups.view', $group->id);
    }

    public function delete(Group $group): RedirectResponse
    {
        $this->authorize('delete', $group);

        if ($this->modelDeletionCheckService->cannotDelete(Group::class, $group->id)) {
            $this->addToast('transport.group.messages.cant_delete', 'general.deletion_error', 'error');

            return back();
        }

        $this->groupService->delete($group);

        $this->notifyModelUpdate(ModelType::Group, 'delete');

        return to_route('settings.transport.groups.view');
    }
}
