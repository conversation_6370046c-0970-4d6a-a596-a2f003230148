<?php

namespace App\Http\Controllers\Transport;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\AddResponsiblePassengersRequest;
use App\Http\Requests\Transport\ListResponsiblesRequest;
use App\Http\Requests\Transport\StoreResponsibleRequest;
use App\Http\Requests\Transport\UpdateResponsibleRequest;
use App\Models\Transport\Passenger;
use App\Models\Transport\Responsible;
use App\Services\Transport\ResponsibleService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ResponsibleController extends Controller
{
    public function __construct(
        private readonly ResponsibleService $responsibleService
    ) {}

    public function list(ListResponsiblesRequest $request): Response
    {
        $this->authorize('list', Responsible::class);

        return inertia('Transport/Responsibles/List/Main', [
            'responsibles' => $this->responsibleService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true
            ),
        ]);
    }

    public function view(Responsible $responsible): Response
    {
        $this->authorize('view', $responsible);

        return Inertia::render('Transport/Responsibles/View/Main', [
            'responsible' => $responsible->load('region'),
        ]);
    }

    public function viewPassengers(Responsible $responsible): Response
    {
        $this->authorize('view', $responsible);

        return Inertia::render('Transport/Responsibles/View/Passengers/Main', [
            'responsible' => $responsible->loadMissing(['passengers.location']),
        ]);
    }

    public function addPassengers(Responsible $responsible, AddResponsiblePassengersRequest $request): Response
    {
        $this->authorize('view', $responsible);

        $passengerIds = $request->validated()['passengers_ids'];
        $this->responsibleService->addPassengersFromIds($responsible, $passengerIds);

        return Inertia::render('Transport/Responsibles/View/Passengers/Main', [
            'responsible' => $responsible->loadMissing(['passengers.location']),
        ]);
    }

    public function removePassenger(Responsible $responsible, Passenger $passenger): Response
    {
        $this->authorize('view', $responsible);

        $this->responsibleService->removePassenger($responsible, $passenger);

        return Inertia::render('Transport/Responsibles/View/Passengers/Main', [
            'responsible' => $responsible->loadMissing(['passengers.location']),
        ]);
    }

    public function save(StoreResponsibleRequest $request): RedirectResponse
    {
        $responsible = $this->responsibleService->save($request->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::Responsible, 'create');

        return to_route('transport.responsibles.view', $responsible->id);
    }

    public function update(Responsible $responsible, UpdateResponsibleRequest $updateResponsibleRequest): RedirectResponse
    {
        $this->authorize('update', $responsible);

        $this->responsibleService->update($responsible, $updateResponsibleRequest->validated());
        $this->notifyModelUpdate(ModelType::Responsible, 'update');

        return to_route('transport.responsibles.view', $responsible->id);
    }

    public function delete(Responsible $responsible): RedirectResponse
    {
        $this->authorize('delete', $responsible);

        $this->responsibleService->delete($responsible);
        $this->notifyModelUpdate(ModelType::Responsible, 'delete');

        return to_route('transport.responsibles.list');
    }
}
