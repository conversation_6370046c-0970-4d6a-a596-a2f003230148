<?php

namespace App\Http\Controllers\Transport;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\StoreStationRequest;
use App\Http\Requests\Transport\UpdateStationRequest;
use App\Http\Requests\Transport\UpdateStationsOrderRequest;
use App\Models\Transport\Route;
use App\Models\Transport\Station;
use App\Services\Transport\RouteOptimizationService;
use App\Services\Transport\RouteService;
use App\Services\Transport\StationService;
use Illuminate\Http\RedirectResponse;

class StationController extends Controller
{
    public function __construct(
        private readonly StationService $stationService,
        private readonly RouteService $routeService,
        private readonly RouteOptimizationService $routeOptimizationService,
    ) {}

    public function save(Route $route, StoreStationRequest $request): RedirectResponse
    {
        $this->authorize('update', $route);

        $passengerIds = collect($request->passengers)->pluck('id')->toArray();

        if ($this->routeService->anyIsPassenger($route, $passengerIds)) {
            // todo add error message passenger already exists in route
        }

        if ($route->stations()->count() >= 100) {
            $this->addToast('messages.custom.stations_limit_exceeded');

            return back();
        }

        $attributes = $request->validated();
        $attributes['created_by'] = auth()->id();
        $attributes['company_id'] = auth()->user()->company_id;

        $this->stationService->save($route, $attributes);

        $this->notifyModelUpdate(ModelType::Station);

        return to_route('transport.routes.stations.view', $route->id);
    }

    public function update(UpdateStationRequest $request, Route $route, Station $station): RedirectResponse
    {
        $this->authorize('update', $route);

        $this->stationService->update($route, $station, $request->validated());

        $this->notifyModelUpdate(ModelType::Station, 'update');

        return to_route('transport.routes.stations.view', $route->id);
    }

    public function delete(Route $route, Station $station): RedirectResponse
    {
        $this->authorize('update', $route);

        $this->stationService->delete($station);

        $this->notifyModelUpdate(ModelType::Station, 'delete');

        return to_route('transport.routes.stations.view', $route->id);
    }

    public function updateOrder(Route $route, UpdateStationsOrderRequest $request): void
    {
        $this->authorize('update', $route);

        if (! $route->isStatic()) {
            $this->addToast('messages.custom.cant_reorder', type: 'error');

            return;
        }

        $this->stationService->updateOrder($route, $request->validated()['order']);

        $this->addToast('messages.custom.order_saved');
    }

    public function optimizeSequence(Route $route): void
    {
        $this->routeOptimizationService->optimizeStationsSequence($route);

        $this->notifyModelUpdate(ModelType::Station, 'update');

        $this->addToast('messages.custom.order_saved');
    }
}
