<?php

namespace App\Http\Controllers\Transport;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Geo\ViewRouteMapFiltersRequest;
use App\Http\Requests\Transport\ListRoutesRequest;
use App\Http\Requests\Transport\StoreRouteRequest;
use App\Http\Requests\Transport\UpdateRouteRequest;
use App\Models\Geo\Location;
use App\Models\Transport\Passenger;
use App\Models\Transport\Route;
use App\Services\Transport\RouteOptimizationService;
use App\Services\Transport\RouteService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class RouteController extends Controller
{
    public function __construct(
        private readonly RouteService $routeService,
        private readonly RouteOptimizationService $routeOptimizationService,
    ) {}

    public function list(ListRoutesRequest $request): Response|RedirectResponse
    {
        return inertia('Transport/Routes/List/Main', [
            'routes' => Route::query()
                ->company(auth()->user()->company_id)
                ->filter($request->validated())
                ->with(['origin', 'destination'])
                ->withCount(['stations'])
                ->latest()
                ->paginate()
                ->withQueryString(),
        ]);
    }

    public function view(Route $route): Response|RedirectResponse
    {
        $this->authorize('view', $route);

        return Inertia::render('Transport/Routes/View/Main', [
            'singleRoute' => $route->loadCount('stations')
                ->loadMissing(['origin', 'destination']),
        ]);
    }

    public function save(StoreRouteRequest $request): RedirectResponse
    {
        $this->authorize('create', Route::class);

        $attributes = $request->validated();

        $attributes['created_by'] = auth()->id();
        $attributes['company_id'] = auth()->user()->company_id;

        $route = $this->routeService->save($attributes);

        $this->notifyModelUpdate(ModelType::Route);

        return to_route('transport.routes.view', $route->id);
    }

    public function update(UpdateRouteRequest $request, Route $route): RedirectResponse
    {
        $this->authorize('update', $route);

        $this->routeService->update($route, $request->validated());

        $this->notifyModelUpdate(ModelType::Route);

        return to_route('transport.routes.view', $route->id);
    }

    public function optimize(Route $route): RedirectResponse
    {
        $this->authorize('update', $route);

        $this->routeOptimizationService->optimizeStationsSequence($route, true);

        $this->notifyModelUpdate(ModelType::Route, 'update');

        return to_route('transport.routes.view', $route->id);
    }

    public function delete(Route $route): RedirectResponse
    {
        $this->authorize('delete', $route);

        $this->routeService->delete($route);

        $this->notifyModelUpdate(ModelType::Route, 'delete');

        return to_route('transport.routes.list');
    }

    public function viewMap(Route $route): Response
    {
        return Inertia::render('Transport/Routes/View/Map/Main', [
            'singleRoute' => $route->loadMissing(['origin', 'destination', 'stations.location']),
        ]);
    }

    public function listMap(ViewRouteMapFiltersRequest $request): Response
    {
        return Inertia::render('Geo/Map/Routes/Main', [
            'routes' => Route::query()
                ->company(auth()->user()->company_id)
                ->filter($request->validated())
                ->with(['origin', 'destination', 'stations.location', 'region'])
                ->withCount(['passengers'])
                ->latest()
                ->get(),
            'filters' => $request->validated(),
        ]);
    }

    public function viewStations(Route $route): Response
    {
        $this->authorize('view', $route);

        return Inertia::render('Transport/Routes/View/Stations/Main', [
            'singleRoute' => $route->loadMissing([
                'origin',
                'destination',
                'stations.location',
                'stations.passengers.locations',
            ])->loadCount('stations'),
            'locations' => Location::query()->company(auth()->user()->company_id)
                ->where('model_type', ModelType::getkey(Passenger::class))
                ->get(),
        ]);
    }
}
