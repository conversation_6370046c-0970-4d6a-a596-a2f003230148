<?php

namespace App\Http\Controllers\Transport;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\ListPassengersRequest;
use App\Http\Requests\Transport\StorePassengerRequest;
use App\Http\Requests\Transport\UpdatePassengerRequest;
use App\Models\Transport\Passenger;
use App\Services\Transport\PassengerService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class PassengerController extends Controller
{
    public function __construct(
        private readonly PassengerService $passengerService
    ) {}

    public function list(ListPassengersRequest $request): Response
    {
        $this->authorize('list', Passenger::class);

        return inertia('Transport/Passengers/List/Main', [
            'passengers' => $this->passengerService->get(
                auth()->user(),
                filterData: $request->validated(),
                paginate: true
            ),
        ]);
    }

    public function view(Passenger $passenger): Response
    {
        $this->authorize('view', $passenger);

        return Inertia::render('Transport/Passengers/View/Main', [
            'passenger' => $passenger->load(['group', 'region']),
        ]);
    }

    public function save(StorePassengerRequest $request): RedirectResponse
    {
        $passenger = $this->passengerService->save($request->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::Passenger, 'create');

        return to_route('transport.passengers.view', $passenger->id);
    }

    public function update(Passenger $passenger, UpdatePassengerRequest $updatePassengerRequest): RedirectResponse
    {
        $this->authorize('update', $passenger);

        $this->passengerService->update($passenger, $updatePassengerRequest->validated());
        $this->notifyModelUpdate(ModelType::Passenger, 'update');

        return to_route('transport.passengers.view', $passenger->id);
    }

    public function delete(Passenger $passenger): RedirectResponse
    {
        $this->authorize('delete', $passenger);

        $this->passengerService->delete($passenger);
        $this->notifyModelUpdate(ModelType::Passenger, 'delete');

        return to_route('transport.passengers.list');
    }

    public function viewLocations(Passenger $passenger): Response
    {
        $this->authorize('view', $passenger);

        return Inertia::render('Transport/Passengers/View/Geo/Main', [
            'passenger' => $passenger->loadMissing('locations')->loadCount('locations'),
        ]);
    }

    public function viewResponsibles(Passenger $passenger): Response
    {
        $this->authorize('view', $passenger);

        return Inertia::render('Transport/Passengers/View/Responsible/Main', [
            'passenger' => $passenger->loadMissing('responsibles')->loadCount('responsibles'),
        ]);
    }

    public function viewMap(ListPassengersRequest $request): Response
    {
        $this->authorize('list', Passenger::class);

        return Inertia::render('Geo/Map/Passengers/Main', [
            'passengers' => $this->passengerService->get(
                auth()->user(),
                filterData: $request->validated(),
                filterLocation: true,
                relations: ['location', 'responsibles:id', 'routes']
            ),
        ]);
    }
}
