<?php

namespace App\Http\Controllers\Transport;

use App\Http\Controllers\Controller;
use App\Http\Requests\Transport\ListRidesForCalendarRequest;
use App\Http\Requests\Transport\ListRidesRequest;
use App\Http\Resources\Transport\RideResource;
use App\Models\Transport\Ride;
use App\Services\Transport\RideService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Inertia\ResponseFactory;

class RideController extends Controller
{
    public function __construct(
        private readonly RideService $rideService,
    ) {}

    public function list(ListRidesRequest $request): Response|RedirectResponse
    {
        $this->authorize('list', Ride::class);

        return inertia('Transport/Rides/List/Main', [
            'rides' => Ride::query()
                ->company(auth()->user()->company_id)
                ->filter($request->validated())
                ->with(['driver', 'vehicle', 'stops.station.location', 'stops.station.passengers', 'attendances', 'route.origin', 'route.destination'])
                ->latest()
                ->paginate()
                ->withQueryString(),
        ]);
    }

    public function view(Ride $ride): Response|RedirectResponse
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'route.origin', 'route.destination', 'assistant']),
        ]);
    }

    public function passengers(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Passengers/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'attendances.passenger.location', 'route']),
        ]);
    }

    public function attendees(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Attendees/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'route', 'attendances.passenger']),
        ]);
    }

    public function map(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Map/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'stops.station.location', 'stops.station.passengers', 'attendances', 'route.origin', 'route.destination']),
        ]);
    }

    public function timeline(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Timeline/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'stops.station.location', 'stops.station.passengers', 'route', 'attendances']),
        ]);
    }

    public function reviews(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Reviews/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'route', 'reviews.reviewer']),
        ]);
    }

    public function incidents(Ride $ride): Response
    {
        $this->authorize('view', $ride);

        return Inertia::render('Transport/Rides/View/Incidents/Main', [
            'ride' => $ride->loadMissing(['driver', 'vehicle', 'route', 'incidents.reporter']),
        ]);
    }

    public function calendar(ListRidesForCalendarRequest $request): Response|ResponseFactory
    {
        $user = auth()->user();
        $rides = $this->rideService->getRidesForCalendar($user, $request->getFilters());

        return inertia('Transport/Rides/Calendar/Main', [
            'rides' => RideResource::collection($rides),
            'filters' => $request->getFilters($rides),
        ]);
    }
}
