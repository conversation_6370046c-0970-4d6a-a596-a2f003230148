<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\InvitationUserRequest;
use App\Models\Auth\UserInvitation;
use App\Services\User\UserInvitationService;

class UserInvitationController extends Controller
{
    public function __construct(
        private UserInvitationService $userInvitationService
    ) {}

    public function invite(InvitationUserRequest $invitationUserRequest)
    {
        $userInvitation = UserInvitation::where('email', $invitationUserRequest->email)->first();

        if ($userInvitation) {
            $this->addToast('messages.custom.invitation_already_sent', null, 'error');

            return back();
        } elseif (! $invitationUserRequest->email) {
            $this->addToast('messages.custom.employee_does_not_have_an_email', null, 'error');

            return back();
        }

        $authUser = auth()->user();

        $this->userInvitationService->invite([
            'first_name' => $invitationUserRequest->first_name,
            'last_name' => $invitationUserRequest->last_name,
            'email' => $invitationUserRequest->email,
            'phone' => $invitationUserRequest->phone,
            'region_id' => $invitationUserRequest->region_id,
            'created_by' => $authUser->id,
            'company_id' => $authUser->company_id,
        ], $authUser);

        $this->addToast('messages.custom.invitation_sent_successfully');

        return back();
    }
}
