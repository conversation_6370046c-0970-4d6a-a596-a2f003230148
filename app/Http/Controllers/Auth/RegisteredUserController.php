<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Auth\User;
use App\Models\Auth\UserInvitation;
use App\Models\Company\Company;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    public function create(UserInvitation $userInvitation): Response
    {
        if ($userInvitation->isAccepted()) {
            abort(404);
        }

        return Inertia::render('Auth/Register', [
            'userInvitation' => $userInvitation,
        ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|string|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'user_invitation_id' => 'required|integer',
        ]);

        $userInvitation = UserInvitation::findOrFail($request->user_invitation_id);

        $company = Company::findOrfail($userInvitation->company_id);

        $user = User::create([
            'first_name' => $userInvitation->first_name,
            'last_name' => $userInvitation->last_name,
            'email' => $request->email,
            'phone' => $userInvitation->phone,
            'region_id' => $userInvitation->region_id,
            'password' => Hash::make($request->password),
            'type' => $userInvitation->type,
            'created_by' => $userInvitation->created_by,
            'company_id' => $company->id,
            'locale' => $company->locale,
        ]);

        $user->refresh();

        $userInvitation->update([
            'accepted' => true,
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }
}
