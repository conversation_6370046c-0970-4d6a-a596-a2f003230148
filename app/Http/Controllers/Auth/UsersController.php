<?php

namespace App\Http\Controllers\Auth;

use App\Enums\General\ModelType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\StoreUserRequest;
use App\Http\Requests\Auth\UpdateUserRequest;
use App\Models\Auth\User;
use App\Services\User\UserService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class UsersController extends Controller
{
    public function __construct(
        private readonly UserService $userService
    ) {}

    public function edit(User $user): Response
    {
        $this->authorize('update', $user);

        return Inertia::render('Settings/Company/Users/<USER>', [
            'user' => $user,
            'users' => $this->userService->get(auth()->user()->company_id),
        ]);
    }

    public function view(?User $user = null): Response|RedirectResponse
    {
        $this->authorize('list', User::class);

        if (! $user) {
            if ($user = $this->userService->firstUser(auth()->user()->company_id)) {
                $this->authorize('view', $user);

                return redirect()->route(request()->route()->getName(), $user->id);
            }
        }

        return Inertia::render('Settings/Company/Users/<USER>', [
            'user' => $user->loadMissing('region'),
            'users' => $this->userService->get(auth()->user()->company_id),
        ]);
    }

    public function save(StoreUserRequest $request): RedirectResponse
    {
        $user = $this->userService->save($request->validated(), auth()->user());
        $this->notifyModelUpdate(ModelType::User, 'create');

        return to_route('fleet.users.view', $user->id);
    }

    public function update(User $user, UpdateUserRequest $request): RedirectResponse
    {
        $this->authorize('update', $user);

        $this->userService->update($user, $request->validated());
        $this->notifyModelUpdate(ModelType::User, 'update');

        return to_route('settings.company.users.view', $user->id);
    }

    public function delete(User $user): RedirectResponse
    {
        $this->authorize('delete', $user);
        $this->userService->delete($user);
        $this->notifyModelUpdate(ModelType::User, 'delete');

        return to_route('settings.company.users.view');
    }
}
