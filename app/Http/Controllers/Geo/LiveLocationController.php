<?php

namespace App\Http\Controllers\Geo;

use App\Http\Controllers\Controller;
use App\Http\Requests\Geo\ViewLiveMapFiltersRequest;
use App\Http\Resources\Fleet\DriverResource;
use App\Http\Resources\Transport\RideResource;
use App\Models\Fleet\Driver;
use App\Services\Fleet\DriverService;
use App\Services\Transport\RideService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Inertia\Inertia;
use Inertia\Response;

class LiveLocationController extends Controller
{
    public function __construct(
        private readonly DriverService $driverService,
        private readonly RideService $rideService,
    ) {}

    public function liveMap(ViewLiveMapFiltersRequest $request): Response
    {
        return Inertia::render('Geo/Map/Main');
    }

    public function drivers(): AnonymousResourceCollection
    {

        $drivers = $this->driverService->listForMap(auth()->user()->company);

        return DriverResource::collection($drivers);
    }

    public function driver(Driver $driver): DriverResource
    {
        return DriverResource::make($this->driverService->getForMap($driver));
    }

    public function driversRide(Driver $driver): RideResource
    {
        $ride = $this->rideService->getForMap($driver);

        return RideResource::make($ride);
    }
}
