<?php

namespace App\Http\Controllers\Geo;

use App\Enums\General\Models;
use App\Http\Controllers\Controller;
use App\Http\Requests\Geo\ListLocationsRequest;
use App\Http\Requests\Geo\StoreLocationRequest;
use App\Http\Requests\Geo\UpdateLocationRequest;
use App\Http\Resources\Geo\LocationResource;
use App\Models\Geo\Location;
use App\Services\Geo\LocationService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class LocationController extends Controller
{
    public function __construct(
        private readonly LocationService $locationService,
    ) {}

    public function list(ListLocationsRequest $request): Response|RedirectResponse
    {
        $query = Location::query()->company(auth()->user()->company_id);

        return inertia('Geo/Locations/List/Main', [
            'locations' => $query->filter($request->validated())
                ->orderBy('updated_at', 'desc')
                ->paginate()
                ->withQueryString(),
        ]);
    }

    /**
     * Display the specified location.
     *
     * @return Response
     */
    public function view(Location $location): Response|RedirectResponse
    {
        return Inertia::render('Geo/Locations/View/Main', [
            'singleLocation' => $location,
        ]);
    }

    /**
     * Fetch the specified location and return as JSON.
     */
    public function get(Location $location): JsonResponse
    {
        return response()->json([
            'location' => LocationResource::make($location),
        ]);
    }

    /**
     * @throws InvalidEnumMemberException
     */
    public function save(StoreLocationRequest $request): JsonResponse
    {
        $attributes = $request->validated();
        $attributes['company_id'] = auth()->user()->company_id;
        $attributes['creator_type'] = Models::User;
        $attributes['creator_id'] = auth()->id();

        $location = $this->locationService->save($attributes);

        $this->addToast(__('messages.success.create', ['model' => __('geo.location.name')]));

        return response()->json([
            'newLocation' => $location,
            'message' => __('messages.success.create', ['model' => __('geo.location.name')]),
        ]);
    }

    /**
     * @throws InvalidEnumMemberException
     */
    public function update(Location $location, UpdateLocationRequest $request): JsonResponse
    {
        $this->locationService->update($location, $request->validated());

        $this->addToast(__('messages.success.update', ['model' => __('geo.location.name')]));

        return response()->json([
            'message' => __('messages.success.update', ['model' => __('geo.location.name')]),
        ]);
    }

    /**
     * @throws InvalidEnumMemberException
     */
    public function delete(Location $location): RedirectResponse
    {
        $locationId = $location->id;
        $this->locationService->delete($location);

        $this->addToast(__('messages.success.delete', ['model' => __('geo.location.name')]));

        $referrer = request()->headers->get('referer');

        if ($referrer && str_contains($referrer, route('geo.locations.view', $locationId))) {
            return redirect()->route('geo.locations.list');
        } else {
            return back();
        }
    }
}
