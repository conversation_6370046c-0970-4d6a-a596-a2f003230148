<?php

namespace App\Http\Controllers\Analytics;

use App\Http\Controllers\Controller;
use App\Http\Requests\Analytics\GetReportRequest;
use App\Http\Resources\Analytics\BarChartResource;
use App\Http\Resources\Analytics\LineChartResource;
use App\Http\Resources\Analytics\PieChartResource;
use App\Http\Resources\Analytics\ScoreChartResource;
use App\Models\Company\Company;
use App\Services\Analytics\Fleet\FleetAnalyticsService;
use App\Services\Analytics\Transport\TransportAnalyticsService;
use Illuminate\Http\JsonResponse;
use Inertia\Response;

class AnalyticsController extends Controller
{
    private TransportAnalyticsService $transportReportService;

    private FleetAnalyticsService $fleetReportService;

    public function __construct(
        TransportAnalyticsService $transportReportService,
        FleetAnalyticsService $fleetReportService
    ) {
        $this->transportReportService = $transportReportService;
        $this->fleetReportService = $fleetReportService;
    }

    public function view(): Response
    {
        return inertia('Analytics/Transport/Main');
    }

    public function viewTransport(): Response
    {
        return inertia('Analytics/Transport/Main');
    }

    public function viewFleet(): Response
    {
        return inertia('Analytics/Fleet/Main');
    }

    // todo move to service
    public function getTransportReport(GetReportRequest $request, string $reportKey): Response|JsonResponse
    {
        $service = $this->transportReportService;
        $filters = $request->filters;
        $company = Company::findOrFail(auth()->user()->company_id);

        $report = match ($reportKey) {
            'total-number-of-rides' => new ScoreChartResource($service->getTotalRides($company, $filters)),
            'ride-duration-metrics' => new BarChartResource($service->getRideDurationMetrics($company, $filters)),
            'ride-distance-metrics' => new BarChartResource($service->getRideDistanceMetrics($company, $filters)),
            'total-number-of-routes' => new ScoreChartResource($service->getTotalNumberOfRoutes($company, $filters)),
            'ridership-per-route' => new PieChartResource($service->getRidershipPerRoute($company, $filters)),
            'on-time-performance' => new LineChartResource($service->getOnTimePerformance($company, $filters)),
            'total-number-of-stations' => new ScoreChartResource($service->getTotalNumberOfStations($company, $filters)),
            'total-number-of-passengers' => new ScoreChartResource($service->getTotalNumberOfPassengers($company, $filters)),
            'ride-status-count' => new ScoreChartResource($service->getRideStatusBreakdownCounts($company, $filters)),
            default => null,
        };

        return is_null($report)
            ? response("No such report as $reportKey", 404)
            : response()->json($report);
    }

    public function getFleetReport(GetReportRequest $request, string $reportKey): Response|JsonResponse
    {
        $service = $this->fleetReportService;
        $filters = $request->filters;
        $company = Company::findOrFail(auth()->user()->company_id);

        $report = match ($reportKey) {
            'driver-performance' => new PieChartResource($service->getDriverPerformanceMetrics($company, $filters)),
            'total-distance-traveled' => new ScoreChartResource($service->getTotalDistanceTraveled($company, $filters)),
            'total-vehicle-count' => new ScoreChartResource($service->getTotalNumberOfVehicles($company, $filters)),
            'total-driver-count' => new ScoreChartResource($service->getTotalNumberOfDrivers($company, $filters)),
            'total-assistant-count' => new ScoreChartResource($service->getTotalNumberOfAssistants($company, $filters)),
            'average-driver-speed' => new BarChartResource($service->getAverageSpeedByDriver($company, $filters)),
            'average-vehicle-usage' => new BarChartResource($service->getAverageVehicleUsageOverTime($company, $filters)),
            'ride-counts-by-driver' => new PieChartResource($service->getRideCountsByDriver($company, $filters)),
            'ride-counts-by-vehicle' => new PieChartResource($service->getRideCountsByVehicle($company, $filters)),
            'total-number-of-drivers' => new ScoreChartResource($service->getTotalNumberOfDrivers($company, $filters)),
            'total-number-of-vehicles' => new ScoreChartResource($service->getTotalNumberOfVehicles($company, $filters)),
            default => null,
        };

        return is_null($report)
            ? response("No such report as $reportKey", 404)
            : response()->json($report);
    }
}
