<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Timezone
{
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check() and $timezone = auth()->user()->company->timezone) {
            date_default_timezone_set($timezone);
        }

        return $next($request);
    }
}
