<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserTypeIs
{
    public function handle(Request $request, Closure $next, string $userType): Response
    {
        $roles = explode('|', $userType);
        $userRole = strtolower(auth()->user()?->type());

        if (in_array($userRole, $roles)) {
            return $next($request);
        }
        abort(Response::HTTP_FORBIDDEN);
    }
}
