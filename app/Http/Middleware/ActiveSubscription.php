<?php

namespace App\Http\Middleware;

use App\Enums\Company\SubscriptionStatus;
use App\Models\Subscription\Subscription;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ActiveSubscription
{
    public function handle(Request $request, Closure $next): Response
    {
        //        $companyIdId = auth()->user()->company_id;

        //        $activeSubscription = Cache::remember(Subscription::key($companyId), now()->addMinutes(10), function () use ($companyId) {
        //            return Subscription::where('company_id', $companyId)
        //                ->where('status', SubscriptionStatus::Active)
        //                ->where('starts_at', '<=', now())
        //                ->where('ends_at', '>=', now())
        //                ->first();
        //        });

        // if ($activeSubscription) {
        //     return $next($request);
        // }

        // // todo to billing page or contact us
        // abort(403);

        return $next($request);
    }
}
