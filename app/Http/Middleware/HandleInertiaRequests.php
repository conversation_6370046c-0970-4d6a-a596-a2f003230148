<?php

namespace App\Http\Middleware;

use App\Enums\General\Locale;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Tightenco\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'app';

    public function share(Request $request): array
    {
        app()->setLocale($request->user()?->locale ?? $request->cookie('locale') ?? Locale::English);

        return [
            ...parent::share($request),
            'ziggy' => function () use ($request) {
                return [
                    ...(new Ziggy)->toArray(),
                    'location' => $request->url(),
                ];
            },
            'csrf' => csrf_token(),
            'user' => [
                'id' => $request->user()?->id,
                'name' => $request->user()?->name,
                'image' => $request->user()?->image,
                'locale' => $request->user()?->locale,
                'company' => [
                    'id' => $request->user()?->company_id,
                    'name' => $request->user()?->company?->name,
                    'logo' => $request->user()?->company?->logo,
                ],
            ],
            'toast' => $request->session()->get('toast'),
            'locale' => app()->getLocale(),
        ];
    }
}
