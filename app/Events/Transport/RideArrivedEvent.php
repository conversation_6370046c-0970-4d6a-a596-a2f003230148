<?php

namespace App\Events\Transport;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RideArrivedEvent implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(private readonly array $attributes) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('ride.'.$this->attributes['ride_id']),
        ];
    }

    public function broadcastAs(): string
    {
        return 'ride.arrived';
    }
}
