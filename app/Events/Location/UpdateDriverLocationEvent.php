<?php

namespace App\Events\Location;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpdateDriverLocationEvent implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(public array $attributes) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('ride.'.$this->attributes['ride_id']),
            new PrivateChannel('company.'.$this->attributes['company_id']),
        ];
    }

    public function broadcastAs(): string
    {
        return 'driver-location.update';
    }

    public function broadcastWith(): array
    {
        return [
            'driver_id' => $this->attributes['driver_id'],
            'company_id' => $this->attributes['company_id'],
            'lng' => $this->attributes['lng'],
            'lat' => $this->attributes['lat'],
            'ride_id' => $this->attributes['ride_id'],
            'etas' => $this->attributes['etas'],
        ];
    }
}
