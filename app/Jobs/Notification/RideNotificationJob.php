<?php

/** @noinspection ALL */

namespace App\Jobs\Notification;

use App\Enums\General\Models;
use App\Enums\System\Queue;
use App\Models\Transport\Ride;
use App\Notifications\Transport\ResponsibleNotification;
use App\Services\Auth\AuthService;
use App\Services\Transport\ResponsibleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class RideNotificationJob implements ShouldQueue
{
    use Queueable;

    public Collection $passengerIds;

    public function __construct(
        Collection $passengers,
        public string $type,
        public array $attributes,
        public array $skip = []
    ) {
        $this->onQueue(Queue::Notifications);
        $this->passengerIds = $passengers->pluck('id')->unique();
    }

    public function handle(): void
    {
        if ($this->passengerIds->isEmpty()) {
            return;
        }

        $authService = app()->make(AuthService::class);
        $ride = Ride::find($this->attributes['ride_id']);

        if (! $this->skipRole(Models::Passenger)) {
            PassengerNotificationJob::dispatch(
                $authService->getAuthenticatedUsers(Models::Passenger, $this->passengerIds),
                $this->type,
                [
                    ...$this->attributes,
                    'request_id' => Str::uuid()->toString(),
                ]
            );
        }

        if ($this->skipRole(Models::Responsible)) {
            return;
        }

        $responsibles = $this->getResponsibleForPassengers($this->passengerIds);

        $authenticatedResponsibleIds = $authService->getAuthenticatedUsers(
            Models::Responsible,
            $responsibles->pluck('id')->unique()
        );

        $responsibles
            ->whereIn('id', $authenticatedResponsibleIds)
            ->each(function ($responsible) use ($ride) {
                Notification::send($responsible, new ResponsibleNotification(
                    $this->type,
                    [
                        ...$this->attributes,
                        'user_type' => Models::Responsible,
                        'locale' => $responsible->locale,
                        'ride_mode' => $ride->mode,
                        'names' => $responsible->passengers->pluck('first_name')->unique()->toArray(),
                        'request_id' => Str::uuid()->toString(),
                    ]
                ));
            });
    }

    public function getResponsibleForPassengers(Collection $passengerIds): Collection
    {
        return app()->make(ResponsibleService::class)->getForPassengerIds($passengerIds->toArray());
    }

    private function skipRole(string $roleType): bool
    {
        foreach ($this->skip as $role) {
            if ($role === $roleType) {
                return true;
            }
        }

        return false;
    }
}
