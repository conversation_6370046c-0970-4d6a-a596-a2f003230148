<?php

namespace App\Dto;

class Address
{
    public function __construct(
        public ?string $country,
        public ?string $streetLine1,
        public ?string $streetLine2,
        public ?string $city,
        public ?string $state,
        public ?string $postalCode,
    ) {}

    public function toArray(): array
    {
        return [
            'street_line_1' => $this->streetLine1,
            'street_line_2' => $this->streetLine2,
            'postal_code' => $this->postalCode,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
        ];
    }
}
